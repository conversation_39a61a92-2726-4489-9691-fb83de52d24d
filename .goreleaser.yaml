version: 2

before:
  hooks:
    - go mod tidy
    - chmod +x ./.build/build-assets.sh
    - ./.build/build-assets.sh {{.Version}}

builds:
  - env:
      - CGO_ENABLED=0

    binary: cloudreve

    ldflags:
      - -s -w
      - -X 'github.com/cloudreve/Cloudreve/v4/application/constants.BackendVersion={{.Tag}}' -X 'github.com/cloudreve/Cloudreve/v4/application/constants.LastCommit={{.ShortCommit}}'

    goos:
      - linux
      - windows
      - darwin
      - freebsd

    goarch:
      - amd64
      - arm
      - arm64
      - loong64

    goarm:
      - 5
      - 6
      - 7

    ignore:
      - goos: windows
        goarm: 5
      - goos: windows
        goarm: 6
      - goos: windows
        goarm: 7
      - goos: windows
        goarch: loong64
      - goos: freebsd
        goarch: loong64
      - goos: freebsd
        goarch: arm

archives:
  - formats: ["tar.gz"]
    # this name template makes the OS and Arch compatible with the results of uname.
    name_template: >-
      cloudreve_{{.Tag}}_{{- .<PERSON><PERSON> }}_{{ .Arch }}
      {{- if .Arm }}v{{ .Arm }}{{ end }}
    # use zip for windows archives
    format_overrides:
      - goos: windows
        formats: ["zip"]

checksum:
  name_template: "checksums.txt"
snapshot:
  version_template: "{{ incpatch .Version }}-next"

changelog:
  sort: asc
  filters:
    exclude:
      - "^docs:"
      - "^test:"

release:
  draft: true
  prerelease: auto
  target_commitish: "{{ .Commit }}"
  name_template: "{{.Version}}"

dockers:
  - dockerfile: Dockerfile
    use: buildx
    build_flag_templates:
      - "--platform=linux/amd64"
    goos: linux
    goarch: amd64
    goamd64: v1
    extra_files:
      - .build/aria2.supervisor.conf
      - .build/entrypoint.sh
    image_templates:
      - "cloudreve/cloudreve:{{ .Tag }}-amd64"
  - dockerfile: Dockerfile
    use: buildx
    build_flag_templates:
      - "--platform=linux/arm64"
    goos: linux
    goarch: arm64
    extra_files:
      - .build/aria2.supervisor.conf
      - .build/entrypoint.sh
    image_templates:
      - "cloudreve/cloudreve:{{ .Tag }}-arm64"

docker_manifests:
  - name_template: "cloudreve/cloudreve:latest"
    image_templates:
      - "cloudreve/cloudreve:{{ .Tag }}-amd64"
      - "cloudreve/cloudreve:{{ .Tag }}-arm64"
  - name_template: "cloudreve/cloudreve:{{ .Tag }}"
    image_templates:
      - "cloudreve/cloudreve:{{ .Tag }}-amd64"
      - "cloudreve/cloudreve:{{ .Tag }}-arm64"

package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"testing"
	"time"
)

const (
	BaseURL = "http://localhost:5212"
	APIURL  = BaseURL + "/api/v4"
)

// 通用响应结构
type APIResponse struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

// 登录响应数据
type LoginData struct {
	User  interface{} `json:"user"`
	Token struct {
		AccessToken string `json:"access_token"`
	} `json:"token"`
}

// 实名认证状态响应
type RealNameAuthStatus struct {
	Verified     bool   `json:"verified"`
	RealName     string `json:"real_name,omitempty"`
	IDCard       string `json:"id_card,omitempty"`
	Status       string `json:"status,omitempty"`
	SubmittedAt  int64  `json:"submitted_at,omitempty"`
	VerifiedAt   int64  `json:"verified_at,omitempty"`
	RejectReason string `json:"reject_reason,omitempty"`
}

// 实名认证提交数据
type RealNameAuthSubmit struct {
	RealName string `json:"real_name"`
	IDCard   string `json:"id_card"`
}

// 用户注册数据
type UserRegister struct {
	Email    string `json:"email"`
	Password string `json:"password"`
	Language string `json:"language"`
}

// 用户登录数据
type UserLogin struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

// 发送HTTP请求的辅助函数
func sendRequest(method, url string, data interface{}, token string) (*http.Response, error) {
	var body io.Reader
	if data != nil {
		jsonData, err := json.Marshal(data)
		if err != nil {
			return nil, err
		}
		body = bytes.NewBuffer(jsonData)
	}

	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}

	client := &http.Client{Timeout: 10 * time.Second}
	return client.Do(req)
}

// 解析API响应
func parseResponse(resp *http.Response, target interface{}) (*APIResponse, error) {
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, err
	}

	if target != nil && apiResp.Data != nil {
		dataBytes, err := json.Marshal(apiResp.Data)
		if err != nil {
			return &apiResp, err
		}
		if err := json.Unmarshal(dataBytes, target); err != nil {
			return &apiResp, err
		}
	}

	return &apiResp, nil
}

// 测试用户注册
func TestUserRegister(t *testing.T) {
	fmt.Println("=== 测试用户注册 ===")
	
	registerData := UserRegister{
		Email:    "<EMAIL>",
		Password: "123456",
		Language: "zh-CN",
	}

	resp, err := sendRequest("POST", APIURL+"/user", registerData, "")
	if err != nil {
		t.Fatalf("发送注册请求失败: %v", err)
	}

	// 先读取原始响应内容
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatalf("读取注册响应失败: %v", err)
	}

	fmt.Printf("注册响应状态码: %d\n", resp.StatusCode)
	fmt.Printf("注册响应原始内容: %s\n", string(body))

	if resp.StatusCode == 404 {
		fmt.Println("⚠️ 注册API路径不存在，可能注册功能已关闭")
		return
	}

	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		t.Errorf("解析注册响应失败: %v", err)
		return
	}

	fmt.Printf("注册响应: code=%d, msg=%s\n", apiResp.Code, apiResp.Msg)

	if resp.StatusCode == 200 && (apiResp.Code == 0 || apiResp.Code == 40001 || apiResp.Code == 40032) {
		if apiResp.Code == 0 {
			fmt.Println("✅ 用户注册成功")
		} else {
			fmt.Println("✅ 用户已存在，跳过注册")
		}
	} else {
		t.Errorf("❌ 用户注册失败: %s", apiResp.Msg)
	}
}

// 测试用户登录
func testUserLogin(t *testing.T) string {
	fmt.Println("\n=== 测试用户登录 ===")
	
	loginData := UserLogin{
		Email:    "<EMAIL>", // 使用刚注册的用户
		Password: "123456",           // 注册时使用的密码
	}

	resp, err := sendRequest("POST", APIURL+"/session/token", loginData, "")
	if err != nil {
		t.Fatalf("发送登录请求失败: %v", err)
	}

	// 先读取原始响应内容
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatalf("读取登录响应失败: %v", err)
	}

	fmt.Printf("登录响应状态码: %d\n", resp.StatusCode)
	fmt.Printf("登录响应原始内容: %s\n", string(body))

	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		t.Fatalf("解析登录响应失败: %v", err)
	}

	fmt.Printf("登录响应: code=%d, msg=%s\n", apiResp.Code, apiResp.Msg)

	var loginResult LoginData
	if apiResp.Data != nil {
		dataBytes, err := json.Marshal(apiResp.Data)
		if err != nil {
			t.Fatalf("序列化登录数据失败: %v", err)
		}
		if err := json.Unmarshal(dataBytes, &loginResult); err != nil {
			t.Fatalf("解析登录数据失败: %v", err)
		}
	}

	if resp.StatusCode == 200 && apiResp.Code == 0 {
		fmt.Println("✅ 用户登录成功")
		fmt.Printf("Token: %s\n", loginResult.Token.AccessToken)
		return loginResult.Token.AccessToken
	} else {
		t.Fatalf("❌ 用户登录失败: %s", apiResp.Msg)
	}
	return ""
}

// 测试查询实名认证状态
func testRealNameAuthStatus(t *testing.T, token string) *RealNameAuthStatus {
	fmt.Println("\n=== 测试查询实名认证状态 ===")
	
	resp, err := sendRequest("GET", APIURL+"/user/verification", nil, token)
	if err != nil {
		t.Fatalf("发送查询请求失败: %v", err)
	}

	var authStatus RealNameAuthStatus
	apiResp, err := parseResponse(resp, &authStatus)
	if err != nil {
		t.Fatalf("解析查询响应失败: %v", err)
	}

	fmt.Printf("查询状态响应码: %d\n", resp.StatusCode)
	fmt.Printf("查询状态响应: code=%d, msg=%s\n", apiResp.Code, apiResp.Msg)

	if resp.StatusCode == 200 && apiResp.Code == 0 {
		fmt.Println("✅ 查询实名认证状态成功")
		fmt.Printf("认证状态: verified=%t, status=%s\n", authStatus.Verified, authStatus.Status)
		if authStatus.RealName != "" {
			fmt.Printf("真实姓名: %s\n", authStatus.RealName)
		}
		if authStatus.IDCard != "" {
			fmt.Printf("身份证号: %s\n", authStatus.IDCard)
		}
		return &authStatus
	} else {
		t.Errorf("❌ 查询实名认证状态失败: %s", apiResp.Msg)
	}
	return nil
}

// 测试提交实名认证
func testSubmitRealNameAuth(t *testing.T, token string) {
	fmt.Println("\n=== 测试提交实名认证 ===")
	
	authData := RealNameAuthSubmit{
		RealName: "张三",
		IDCard:   "110101199001011234",
	}

	resp, err := sendRequest("POST", APIURL+"/user/verification", authData, token)
	if err != nil {
		t.Fatalf("发送认证请求失败: %v", err)
	}

	apiResp, err := parseResponse(resp, nil)
	if err != nil {
		t.Fatalf("解析认证响应失败: %v", err)
	}

	fmt.Printf("提交认证响应码: %d\n", resp.StatusCode)
	fmt.Printf("提交认证响应: code=%d, msg=%s\n", apiResp.Code, apiResp.Msg)

	if resp.StatusCode == 200 && apiResp.Code == 0 {
		fmt.Println("✅ 提交实名认证成功")
	} else {
		t.Errorf("❌ 提交实名认证失败: %s", apiResp.Msg)
	}
}

// 测试未认证用户上传文件
func testFileUploadWithoutAuth(t *testing.T, token string) {
	fmt.Println("\n=== 测试未认证用户上传文件 ===")
	
	uploadData := map[string]interface{}{
		"path":      "/",
		"size":      100,
		"name":      "test.txt",
		"policy_id": 1,
	}

	resp, err := sendRequest("PUT", APIURL+"/file/upload", uploadData, token)
	if err != nil {
		t.Fatalf("发送上传请求失败: %v", err)
	}

	apiResp, err := parseResponse(resp, nil)
	if err != nil {
		t.Fatalf("解析上传响应失败: %v", err)
	}

	fmt.Printf("创建上传会话响应码: %d\n", resp.StatusCode)
	fmt.Printf("创建上传会话响应: code=%d, msg=%s\n", apiResp.Code, apiResp.Msg)

	if resp.StatusCode == 200 && apiResp.Code == 40088 {
		fmt.Println("✅ 实名认证检查生效，未认证用户无法上传文件")
	} else {
		t.Errorf("❌ 实名认证检查未生效，预期错误码40088，实际: %d", apiResp.Code)
	}
}

// 主测试函数
func TestRealNameAuthFlow(t *testing.T) {
	fmt.Println("🚀 开始测试 Cloudreve 实名认证功能")

	// 1. 测试用户注册（可能失败，但不影响后续测试）
	TestUserRegister(t)

	// 2. 测试用户登录（使用默认管理员账户）
	token := testUserLogin(t)
	if token == "" {
		t.Fatal("❌ 无法获取登录令牌，测试终止")
	}

	// 3. 测试查询实名认证状态（应该为空或未认证）
	_ = testRealNameAuthStatus(t, token)

	// 4. 测试未认证用户上传文件（应该被拒绝）
	testFileUploadWithoutAuth(t, token)

	// 5. 测试提交实名认证
	testSubmitRealNameAuth(t, token)

	// 6. 再次查询实名认证状态（应该显示待审核）
	fmt.Println("\n=== 再次查询实名认证状态 ===")
	testRealNameAuthStatus(t, token)
	
	fmt.Println("\n🎉 实名认证功能测试完成！")
}

package user

import (
	"strings"
	"time"

	"github.com/cloudreve/Cloudreve/v4/application/dependency"
	"github.com/cloudreve/Cloudreve/v4/ent"
	"github.com/cloudreve/Cloudreve/v4/inventory"
	"github.com/cloudreve/Cloudreve/v4/inventory/types"
	"github.com/cloudreve/Cloudreve/v4/pkg/hashid"
	"github.com/cloudreve/Cloudreve/v4/pkg/serializer"
	"github.com/gin-gonic/gin"
)

// RealNameAuthService 实名认证服务
type RealNameAuthService struct {
	RealName string `json:"real_name" binding:"required,min=2,max=50"`
	IDCard   string `json:"id_card" binding:"required,len=18"`
}

type RealNameAuthServiceParamsCtx struct{}

// Submit 提交实名认证
func (service *RealNameAuthService) Submit(c *gin.Context) error {
	dep := dependency.FromContext(c)
	u := inventory.UserFromContext(c)
	userClient := dep.UserClient()

	realName := strings.TrimSpace(service.RealName)
	idCard := strings.TrimSpace(service.IDCard)

	// 验证姓名格式（中文姓名）
	if len(realName) < 2 || len(realName) > 50 {
		return serializer.NewError(serializer.CodeParamErr, "请输入有效的中文姓名", nil)
	}

	// 验证身份证号格式
	if len(idCard) != 18 {
		return serializer.NewError(serializer.CodeParamErr, "请输入有效的身份证号", nil)
	}

	// 检查是否已经认证通过
	if u.Settings.RealNameAuth != nil && u.Settings.RealNameAuth.Status == "approved" {
		return serializer.NewError(serializer.CodeParamErr, "您已完成实名认证，无需重复提交", nil)
	}

	// 检查是否有待审核的认证
	if u.Settings.RealNameAuth != nil && u.Settings.RealNameAuth.Status == "pending" {
		return serializer.NewError(serializer.CodeParamErr, "您的实名认证正在审核中，请耐心等待", nil)
	}

	// 初始化实名认证信息
	if u.Settings.RealNameAuth == nil {
		u.Settings.RealNameAuth = &types.RealNameAuth{}
	}

	// 更新实名认证信息
	u.Settings.RealNameAuth.RealName = realName
	u.Settings.RealNameAuth.IDCard = idCard
	u.Settings.RealNameAuth.Status = "pending"
	u.Settings.RealNameAuth.SubmittedAt = time.Now().Unix()
	u.Settings.RealNameAuth.ApprovedAt = 0
	u.Settings.RealNameAuth.RejectedAt = 0
	u.Settings.RealNameAuth.RejectReason = ""

	// 保存设置
	if err := userClient.SaveSettings(c, u); err != nil {
		return serializer.NewError(serializer.CodeDBError, "Failed to save real name auth", err)
	}

	return nil
}

// GetStatus 获取实名认证状态
func GetRealNameAuthStatus(c *gin.Context) (*RealNameAuthResponse, error) {
	u := inventory.UserFromContext(c)

	if u.Settings.RealNameAuth == nil {
		return &RealNameAuthResponse{
			Status: "not_submitted",
		}, nil
	}

	response := &RealNameAuthResponse{
		Status:       u.Settings.RealNameAuth.Status,
		SubmittedAt:  u.Settings.RealNameAuth.SubmittedAt,
		ApprovedAt:   u.Settings.RealNameAuth.ApprovedAt,
		RejectedAt:   u.Settings.RealNameAuth.RejectedAt,
		RejectReason: u.Settings.RealNameAuth.RejectReason,
	}

	// 脱敏处理姓名和身份证号
	if u.Settings.RealNameAuth.RealName != "" {
		response.RealName = maskName(u.Settings.RealNameAuth.RealName)
	}
	if u.Settings.RealNameAuth.IDCard != "" {
		response.IDCard = maskIDCard(u.Settings.RealNameAuth.IDCard)
	}

	return response, nil
}

// IsRealNameVerified 检查用户是否已完成实名认证
func IsRealNameVerified(u *ent.User) bool {
	return u.Settings.RealNameAuth != nil && u.Settings.RealNameAuth.Status == "approved"
}

// AdminApproveRealNameAuth 管理员审核实名认证（通过）
type AdminApproveRealNameAuthService struct {
	UserID string `json:"user_id" binding:"required"`
}

type AdminApproveRealNameAuthServiceParamsCtx struct{}

func (service *AdminApproveRealNameAuthService) Approve(c *gin.Context) error {
	dep := dependency.FromContext(c)
	userClient := dep.UserClient()
	hasher := dep.HashIDEncoder()

	// 解码用户ID
	uid, err := hasher.Decode(service.UserID, hashid.UserID)
	if err != nil {
		return serializer.NewError(serializer.CodeParamErr, "Invalid user ID", err)
	}

	// 获取用户
	user, err := userClient.GetByID(c, uid)
	if err != nil {
		return serializer.NewError(serializer.CodeUserNotFound, "User not found", err)
	}

	// 检查是否有待审核的认证
	if user.Settings.RealNameAuth == nil || user.Settings.RealNameAuth.Status != "pending" {
		return serializer.NewError(serializer.CodeParamErr, "No pending real name auth found", nil)
	}

	// 更新认证状态
	user.Settings.RealNameAuth.Status = "approved"
	user.Settings.RealNameAuth.ApprovedAt = time.Now().Unix()
	user.Settings.RealNameAuth.RejectedAt = 0
	user.Settings.RealNameAuth.RejectReason = ""

	// 保存设置
	if err := userClient.SaveSettings(c, user); err != nil {
		return serializer.NewError(serializer.CodeDBError, "Failed to approve real name auth", err)
	}

	return nil
}

// AdminRejectRealNameAuth 管理员审核实名认证（拒绝）
type AdminRejectRealNameAuthService struct {
	UserID string `json:"user_id" binding:"required"`
	Reason string `json:"reason" binding:"required,min=1,max=200"`
}

type AdminRejectRealNameAuthServiceParamsCtx struct{}

func (service *AdminRejectRealNameAuthService) Reject(c *gin.Context) error {
	dep := dependency.FromContext(c)
	userClient := dep.UserClient()
	hasher := dep.HashIDEncoder()

	// 解码用户ID
	uid, err := hasher.Decode(service.UserID, hashid.UserID)
	if err != nil {
		return serializer.NewError(serializer.CodeParamErr, "Invalid user ID", err)
	}

	// 获取用户
	user, err := userClient.GetByID(c, uid)
	if err != nil {
		return serializer.NewError(serializer.CodeUserNotFound, "User not found", err)
	}

	// 检查是否有待审核的认证
	if user.Settings.RealNameAuth == nil || user.Settings.RealNameAuth.Status != "pending" {
		return serializer.NewError(serializer.CodeParamErr, "No pending real name auth found", nil)
	}

	// 更新认证状态
	user.Settings.RealNameAuth.Status = "rejected"
	user.Settings.RealNameAuth.RejectedAt = time.Now().Unix()
	user.Settings.RealNameAuth.ApprovedAt = 0
	user.Settings.RealNameAuth.RejectReason = service.Reason

	// 保存设置
	if err := userClient.SaveSettings(c, user); err != nil {
		return serializer.NewError(serializer.CodeDBError, "Failed to reject real name auth", err)
	}

	return nil
}

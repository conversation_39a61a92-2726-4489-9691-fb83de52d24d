// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/cloudreve/Cloudreve/v4/ent/davaccount"
	"github.com/cloudreve/Cloudreve/v4/ent/user"
	"github.com/cloudreve/Cloudreve/v4/inventory/types"
	"github.com/cloudreve/Cloudreve/v4/pkg/boolset"
)

// DavAccount is the model entity for the DavAccount schema.
type DavAccount struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt *time.Time `json:"deleted_at,omitempty"`
	// Name holds the value of the "name" field.
	Name string `json:"name,omitempty"`
	// URI holds the value of the "uri" field.
	URI string `json:"uri,omitempty"`
	// Password holds the value of the "password" field.
	Password string `json:"-"`
	// Options holds the value of the "options" field.
	Options *boolset.BooleanSet `json:"options,omitempty"`
	// Props holds the value of the "props" field.
	Props *types.DavAccountProps `json:"props,omitempty"`
	// OwnerID holds the value of the "owner_id" field.
	OwnerID int `json:"owner_id,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the DavAccountQuery when eager-loading is set.
	Edges        DavAccountEdges `json:"edges"`
	selectValues sql.SelectValues
}

// DavAccountEdges holds the relations/edges for other nodes in the graph.
type DavAccountEdges struct {
	// Owner holds the value of the owner edge.
	Owner *User `json:"owner,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// OwnerOrErr returns the Owner value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e DavAccountEdges) OwnerOrErr() (*User, error) {
	if e.loadedTypes[0] {
		if e.Owner == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: user.Label}
		}
		return e.Owner, nil
	}
	return nil, &NotLoadedError{edge: "owner"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*DavAccount) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case davaccount.FieldProps:
			values[i] = new([]byte)
		case davaccount.FieldOptions:
			values[i] = new(boolset.BooleanSet)
		case davaccount.FieldID, davaccount.FieldOwnerID:
			values[i] = new(sql.NullInt64)
		case davaccount.FieldName, davaccount.FieldURI, davaccount.FieldPassword:
			values[i] = new(sql.NullString)
		case davaccount.FieldCreatedAt, davaccount.FieldUpdatedAt, davaccount.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the DavAccount fields.
func (da *DavAccount) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case davaccount.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			da.ID = int(value.Int64)
		case davaccount.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				da.CreatedAt = value.Time
			}
		case davaccount.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				da.UpdatedAt = value.Time
			}
		case davaccount.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				da.DeletedAt = new(time.Time)
				*da.DeletedAt = value.Time
			}
		case davaccount.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				da.Name = value.String
			}
		case davaccount.FieldURI:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field uri", values[i])
			} else if value.Valid {
				da.URI = value.String
			}
		case davaccount.FieldPassword:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field password", values[i])
			} else if value.Valid {
				da.Password = value.String
			}
		case davaccount.FieldOptions:
			if value, ok := values[i].(*boolset.BooleanSet); !ok {
				return fmt.Errorf("unexpected type %T for field options", values[i])
			} else if value != nil {
				da.Options = value
			}
		case davaccount.FieldProps:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field props", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &da.Props); err != nil {
					return fmt.Errorf("unmarshal field props: %w", err)
				}
			}
		case davaccount.FieldOwnerID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field owner_id", values[i])
			} else if value.Valid {
				da.OwnerID = int(value.Int64)
			}
		default:
			da.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the DavAccount.
// This includes values selected through modifiers, order, etc.
func (da *DavAccount) Value(name string) (ent.Value, error) {
	return da.selectValues.Get(name)
}

// QueryOwner queries the "owner" edge of the DavAccount entity.
func (da *DavAccount) QueryOwner() *UserQuery {
	return NewDavAccountClient(da.config).QueryOwner(da)
}

// Update returns a builder for updating this DavAccount.
// Note that you need to call DavAccount.Unwrap() before calling this method if this DavAccount
// was returned from a transaction, and the transaction was committed or rolled back.
func (da *DavAccount) Update() *DavAccountUpdateOne {
	return NewDavAccountClient(da.config).UpdateOne(da)
}

// Unwrap unwraps the DavAccount entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (da *DavAccount) Unwrap() *DavAccount {
	_tx, ok := da.config.driver.(*txDriver)
	if !ok {
		panic("ent: DavAccount is not a transactional entity")
	}
	da.config.driver = _tx.drv
	return da
}

// String implements the fmt.Stringer.
func (da *DavAccount) String() string {
	var builder strings.Builder
	builder.WriteString("DavAccount(")
	builder.WriteString(fmt.Sprintf("id=%v, ", da.ID))
	builder.WriteString("created_at=")
	builder.WriteString(da.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(da.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := da.DeletedAt; v != nil {
		builder.WriteString("deleted_at=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(da.Name)
	builder.WriteString(", ")
	builder.WriteString("uri=")
	builder.WriteString(da.URI)
	builder.WriteString(", ")
	builder.WriteString("password=<sensitive>")
	builder.WriteString(", ")
	builder.WriteString("options=")
	builder.WriteString(fmt.Sprintf("%v", da.Options))
	builder.WriteString(", ")
	builder.WriteString("props=")
	builder.WriteString(fmt.Sprintf("%v", da.Props))
	builder.WriteString(", ")
	builder.WriteString("owner_id=")
	builder.WriteString(fmt.Sprintf("%v", da.OwnerID))
	builder.WriteByte(')')
	return builder.String()
}

// SetOwner manually set the edge as loaded state.
func (e *DavAccount) SetOwner(v *User) {
	e.Edges.Owner = v
	e.Edges.loadedTypes[0] = true
}

// DavAccounts is a parsable slice of DavAccount.
type DavAccounts []*DavAccount

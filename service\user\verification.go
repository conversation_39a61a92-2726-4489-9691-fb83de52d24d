package user

import (
	"time"

	"github.com/cloudreve/Cloudreve/v4/application/dependency"
	"github.com/cloudreve/Cloudreve/v4/inventory"
	"github.com/cloudreve/Cloudreve/v4/inventory/types"
	"github.com/cloudreve/Cloudreve/v4/pkg/hashid"
	"github.com/cloudreve/Cloudreve/v4/pkg/serializer"
	"github.com/gin-gonic/gin"
)

// VerificationServiceParamsCtx 实名认证服务参数上下文
type VerificationServiceParamsCtx struct{}

// ApproveVerificationServiceParamsCtx 审核通过服务参数上下文
type ApproveVerificationServiceParamsCtx struct{}

// RejectVerificationServiceParamsCtx 审核拒绝服务参数上下文
type RejectVerificationServiceParamsCtx struct{}

// ListVerificationServiceParamsCtx 认证列表服务参数上下文
type ListVerificationServiceParamsCtx struct{}

// VerificationService 实名认证服务
type VerificationService struct {
	RealName string `json:"real_name" binding:"required,min=2,max=50"`
	IDCard   string `json:"id_card" binding:"required,len=18"`
}

// ApproveVerificationService 审核通过服务
type ApproveVerificationService struct {
	UserID string `json:"user_id" binding:"required"`
}

// RejectVerificationService 审核拒绝服务
type RejectVerificationService struct {
	UserID string `json:"user_id" binding:"required"`
	Reason string `json:"reason" binding:"required,max=200"`
}

// ListVerificationService 认证列表服务
type ListVerificationService struct {
	Page     int    `form:"page" binding:"min=1"`
	PageSize int    `form:"page_size" binding:"min=1,max=100"`
	Status   string `form:"status"`
}

// VerificationData 认证数据结构
type VerificationData struct {
	RealName     string    `json:"real_name"`
	IDCard       string    `json:"id_card"`
	SubmittedAt  time.Time `json:"submitted_at"`
	Status       string    `json:"status"`
	VerifiedAt   *time.Time `json:"verified_at,omitempty"`
	RejectReason string    `json:"reject_reason,omitempty"`
}

// Submit 提交实名认证
func (service *VerificationService) Submit(c *gin.Context) (interface{}, error) {
	currentUser := inventory.UserFromContext(c)
	if currentUser == nil {
		return nil, serializer.NewError(serializer.CodeCredentialInvalid, "用户未登录", nil)
	}

	// 验证姓名格式（中文姓名）
	if !isValidChineseName(service.RealName) {
		return nil, serializer.NewError(serializer.CodeParamErr, "请输入有效的中文姓名", nil)
	}

	// 验证身份证号格式
	if !isValidIDCard(service.IDCard) {
		return nil, serializer.NewError(serializer.CodeParamErr, "请输入有效的身份证号", nil)
	}

	dep := dependency.FromContext(c)
	userClient := dep.UserClient()

	// 检查用户是否已经认证
	user, err := userClient.GetByID(c, currentUser.ID)
	if err != nil {
		return nil, serializer.NewError(serializer.CodeDBError, "查询用户信息失败", err)
	}

	// 检查用户设置中的实名认证状态
	if user.Settings.RealNameAuth != nil && user.Settings.RealNameAuth.Status == "approved" {
		return nil, serializer.NewError(serializer.CodeParamErr, "您已完成实名认证", nil)
	}

	// 更新用户设置中的认证信息
	if user.Settings.RealNameAuth == nil {
		user.Settings.RealNameAuth = &types.RealNameAuth{}
	}
	user.Settings.RealNameAuth.RealName = service.RealName
	user.Settings.RealNameAuth.IDCard = service.IDCard
	user.Settings.RealNameAuth.Status = "pending"
	user.Settings.RealNameAuth.SubmittedAt = time.Now().Unix()

	_, err = user.Update().
		SetSettings(user.Settings).
		Save(c)

	if err != nil {
		return nil, serializer.NewError(serializer.CodeDBError, "保存认证信息失败", err)
	}

	return map[string]interface{}{
		"status":       "pending",
		"submitted_at": user.Settings.RealNameAuth.SubmittedAt,
	}, nil
}

// Approve 审核通过实名认证
func (service *ApproveVerificationService) Approve(c *gin.Context) error {
	// 检查管理员权限
	currentUser := inventory.UserFromContext(c)
	if currentUser == nil || currentUser.Edges.Group.ID != 1 {
		return serializer.NewError(serializer.CodeNoPermissionErr, "无权限执行此操作", nil)
	}

	dep := dependency.FromContext(c)
	userClient := dep.UserClient()
	idEncoder := dep.HashIDEncoder()

	// 解码用户ID
	userID, err := idEncoder.Decode(service.UserID, hashid.UserID)
	if err != nil {
		return serializer.NewError(serializer.CodeParamErr, "无效的用户ID", nil)
	}

	// 查询用户
	user, err := userClient.GetByID(c, userID)
	if err != nil {
		return serializer.NewError(serializer.CodeDBError, "查询用户信息失败", err)
	}

	if user.Settings.RealNameAuth != nil && user.Settings.RealNameAuth.Status == "approved" {
		return serializer.NewError(serializer.CodeParamErr, "用户已完成认证", nil)
	}

	// 更新认证状态
	now := time.Now()
	if user.Settings.RealNameAuth == nil {
		user.Settings.RealNameAuth = &types.RealNameAuth{}
	}
	user.Settings.RealNameAuth.Status = "approved"
	user.Settings.RealNameAuth.ApprovedAt = now.Unix()

	_, err = user.Update().
		SetSettings(user.Settings).
		Save(c)

	if err != nil {
		return serializer.NewError(serializer.CodeDBError, "更新认证状态失败", err)
	}

	return nil
}

// Reject 拒绝实名认证
func (service *RejectVerificationService) Reject(c *gin.Context) error {
	// 检查管理员权限
	currentUser := inventory.UserFromContext(c)
	if currentUser == nil || currentUser.Edges.Group.ID != 1 {
		return serializer.NewError(serializer.CodeNoPermissionErr, "无权限执行此操作", nil)
	}

	dep := dependency.FromContext(c)
	userClient := dep.UserClient()
	idEncoder := dep.HashIDEncoder()

	// 解码用户ID
	userID, err := idEncoder.Decode(service.UserID, hashid.UserID)
	if err != nil {
		return serializer.NewError(serializer.CodeParamErr, "无效的用户ID", nil)
	}

	// 查询用户
	user, err := userClient.GetByID(c, userID)
	if err != nil {
		return serializer.NewError(serializer.CodeDBError, "查询用户信息失败", err)
	}

	if user.Settings.RealNameAuth != nil && user.Settings.RealNameAuth.Status == "approved" {
		return serializer.NewError(serializer.CodeParamErr, "用户已完成认证", nil)
	}

	// 更新认证状态为拒绝
	if user.Settings.RealNameAuth == nil {
		user.Settings.RealNameAuth = &types.RealNameAuth{}
	}
	user.Settings.RealNameAuth.Status = "rejected"
	user.Settings.RealNameAuth.RejectReason = service.Reason
	user.Settings.RealNameAuth.RejectedAt = time.Now().Unix()

	_, err = user.Update().
		SetSettings(user.Settings).
		Save(c)

	if err != nil {
		return serializer.NewError(serializer.CodeDBError, "更新认证状态失败", err)
	}

	return nil
}

// List 获取待审核的实名认证列表
func (service *ListVerificationService) List(c *gin.Context) (interface{}, error) {
	// 检查管理员权限
	currentUser := inventory.UserFromContext(c)
	if currentUser == nil || currentUser.Edges.Group.ID != 1 {
		return nil, serializer.NewError(serializer.CodeNoPermissionErr, "无权限执行此操作", nil)
	}

	// 设置默认值
	if service.Page == 0 {
		service.Page = 1
	}
	if service.PageSize == 0 {
		service.PageSize = 20
	}

	dep := dependency.FromContext(c)
	userClient := dep.UserClient()
	idEncoder := dep.HashIDEncoder()

	// 查询用户列表
	listParams := &inventory.ListUserParameters{
		PaginationArgs: &inventory.PaginationArgs{
			Page:     service.Page - 1, // 转换为0基索引
			PageSize: service.PageSize,
		},
	}
	result, err := userClient.ListUsers(c, listParams)
	if err != nil {
		return nil, serializer.NewError(serializer.CodeDBError, "查询用户列表失败", err)
	}

	// 构建响应数据
	var items []map[string]interface{}
	for _, user := range result.Users {
		// 只显示有实名认证数据的用户
		if user.Settings.RealNameAuth == nil {
			continue
		}

		// 根据状态过滤
		if service.Status != "" && user.Settings.RealNameAuth.Status != service.Status {
			continue
		}

		item := map[string]interface{}{
			"user_id":    hashid.EncodeUserID(idEncoder, user.ID),
			"email":      user.Email,
			"nick":       user.Nick,
			"verified":   user.Settings.RealNameAuth.Status == "approved",
			"created_at": user.CreatedAt,
			"updated_at": user.UpdatedAt,
		}

		if user.Settings.RealNameAuth != nil {
			item["real_name"] = user.Settings.RealNameAuth.RealName
			item["id_card"] = maskIDCard(user.Settings.RealNameAuth.IDCard)
			item["status"] = user.Settings.RealNameAuth.Status
			if user.Settings.RealNameAuth.SubmittedAt != 0 {
				item["submitted_at"] = user.Settings.RealNameAuth.SubmittedAt
			}
			item["reject_reason"] = user.Settings.RealNameAuth.RejectReason
			if user.Settings.RealNameAuth.ApprovedAt != 0 {
				item["verified_at"] = user.Settings.RealNameAuth.ApprovedAt
			}
		}

		items = append(items, item)
	}

	return map[string]interface{}{
		"items": items,
		"total": result.TotalItems,
		"page":  service.Page,
		"page_size": service.PageSize,
	}, nil
}



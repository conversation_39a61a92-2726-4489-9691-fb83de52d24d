trigger:
  tags:
    include:
      - '*'
variables:
  GO_VERSION: "1.23.6"
  NODE_VERSION: "22.x"
  DOCKER_BUILDKIT: 1

pool:
  vmImage: ubuntu-latest

jobs:
  - job: Release
    steps:
      - checkout: self
        submodules: true
        persistCredentials: true
      - task: NodeTool@0 
        inputs:
          versionSpec: '$(NODE_VERSION)'
        displayName: 'Install Node.js'
      - task: GoTool@0
        inputs:
          version: "$(GO_VERSION)"
        displayName: Install Go
      - task: Docker@2
        inputs:
          containerRegistry: "CR DockerHub"
          command: "login"
          addPipelineData: false
          addBaseImageData: false
      - task: CmdLine@2
        displayName: "Install multiarch/qemu-user-static"
        inputs:
          script: |
            docker run --rm --privileged multiarch/qemu-user-static --reset -p yes
      - task: goreleaser@0
        condition: and(succeeded(), startsWith(variables['Build.SourceBranch'], 'refs/tags/'))
        inputs:
          version: "latest"
          distribution: "goreleaser"
          workdir: "$(Build.SourcesDirectory)"
          args: "release --timeout 60m"
        env:
          GITHUB_TOKEN: $(GITHUB_TOKEN)
          
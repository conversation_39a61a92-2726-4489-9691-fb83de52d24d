# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
*.db
*.bin
/release/
application/statics/assets.zip

# Test binary, build with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Development enviroment
.idea/*
uploads/*
temp

# Version control
version.lock

# Config file
*.ini
conf/conf.ini
/statik/
.vscode/

dist/
data/
tmp/
.devcontainer/
cloudreve

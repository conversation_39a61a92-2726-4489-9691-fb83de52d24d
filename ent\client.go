// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"github.com/cloudreve/Cloudreve/v4/ent/migrate"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/cloudreve/Cloudreve/v4/ent/davaccount"
	"github.com/cloudreve/Cloudreve/v4/ent/directlink"
	"github.com/cloudreve/Cloudreve/v4/ent/entity"
	"github.com/cloudreve/Cloudreve/v4/ent/file"
	"github.com/cloudreve/Cloudreve/v4/ent/group"
	"github.com/cloudreve/Cloudreve/v4/ent/metadata"
	"github.com/cloudreve/Cloudreve/v4/ent/node"
	"github.com/cloudreve/Cloudreve/v4/ent/passkey"
	"github.com/cloudreve/Cloudreve/v4/ent/setting"
	"github.com/cloudreve/Cloudreve/v4/ent/share"
	"github.com/cloudreve/Cloudreve/v4/ent/storagepolicy"
	"github.com/cloudreve/Cloudreve/v4/ent/task"
	"github.com/cloudreve/Cloudreve/v4/ent/user"

	stdsql "database/sql"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// DavAccount is the client for interacting with the DavAccount builders.
	DavAccount *DavAccountClient
	// DirectLink is the client for interacting with the DirectLink builders.
	DirectLink *DirectLinkClient
	// Entity is the client for interacting with the Entity builders.
	Entity *EntityClient
	// File is the client for interacting with the File builders.
	File *FileClient
	// Group is the client for interacting with the Group builders.
	Group *GroupClient
	// Metadata is the client for interacting with the Metadata builders.
	Metadata *MetadataClient
	// Node is the client for interacting with the Node builders.
	Node *NodeClient
	// Passkey is the client for interacting with the Passkey builders.
	Passkey *PasskeyClient
	// Setting is the client for interacting with the Setting builders.
	Setting *SettingClient
	// Share is the client for interacting with the Share builders.
	Share *ShareClient
	// StoragePolicy is the client for interacting with the StoragePolicy builders.
	StoragePolicy *StoragePolicyClient
	// Task is the client for interacting with the Task builders.
	Task *TaskClient
	// User is the client for interacting with the User builders.
	User *UserClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.DavAccount = NewDavAccountClient(c.config)
	c.DirectLink = NewDirectLinkClient(c.config)
	c.Entity = NewEntityClient(c.config)
	c.File = NewFileClient(c.config)
	c.Group = NewGroupClient(c.config)
	c.Metadata = NewMetadataClient(c.config)
	c.Node = NewNodeClient(c.config)
	c.Passkey = NewPasskeyClient(c.config)
	c.Setting = NewSettingClient(c.config)
	c.Share = NewShareClient(c.config)
	c.StoragePolicy = NewStoragePolicyClient(c.config)
	c.Task = NewTaskClient(c.config)
	c.User = NewUserClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("ent: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:           ctx,
		config:        cfg,
		DavAccount:    NewDavAccountClient(cfg),
		DirectLink:    NewDirectLinkClient(cfg),
		Entity:        NewEntityClient(cfg),
		File:          NewFileClient(cfg),
		Group:         NewGroupClient(cfg),
		Metadata:      NewMetadataClient(cfg),
		Node:          NewNodeClient(cfg),
		Passkey:       NewPasskeyClient(cfg),
		Setting:       NewSettingClient(cfg),
		Share:         NewShareClient(cfg),
		StoragePolicy: NewStoragePolicyClient(cfg),
		Task:          NewTaskClient(cfg),
		User:          NewUserClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:           ctx,
		config:        cfg,
		DavAccount:    NewDavAccountClient(cfg),
		DirectLink:    NewDirectLinkClient(cfg),
		Entity:        NewEntityClient(cfg),
		File:          NewFileClient(cfg),
		Group:         NewGroupClient(cfg),
		Metadata:      NewMetadataClient(cfg),
		Node:          NewNodeClient(cfg),
		Passkey:       NewPasskeyClient(cfg),
		Setting:       NewSettingClient(cfg),
		Share:         NewShareClient(cfg),
		StoragePolicy: NewStoragePolicyClient(cfg),
		Task:          NewTaskClient(cfg),
		User:          NewUserClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		DavAccount.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	for _, n := range []interface{ Use(...Hook) }{
		c.DavAccount, c.DirectLink, c.Entity, c.File, c.Group, c.Metadata, c.Node,
		c.Passkey, c.Setting, c.Share, c.StoragePolicy, c.Task, c.User,
	} {
		n.Use(hooks...)
	}
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	for _, n := range []interface{ Intercept(...Interceptor) }{
		c.DavAccount, c.DirectLink, c.Entity, c.File, c.Group, c.Metadata, c.Node,
		c.Passkey, c.Setting, c.Share, c.StoragePolicy, c.Task, c.User,
	} {
		n.Intercept(interceptors...)
	}
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *DavAccountMutation:
		return c.DavAccount.mutate(ctx, m)
	case *DirectLinkMutation:
		return c.DirectLink.mutate(ctx, m)
	case *EntityMutation:
		return c.Entity.mutate(ctx, m)
	case *FileMutation:
		return c.File.mutate(ctx, m)
	case *GroupMutation:
		return c.Group.mutate(ctx, m)
	case *MetadataMutation:
		return c.Metadata.mutate(ctx, m)
	case *NodeMutation:
		return c.Node.mutate(ctx, m)
	case *PasskeyMutation:
		return c.Passkey.mutate(ctx, m)
	case *SettingMutation:
		return c.Setting.mutate(ctx, m)
	case *ShareMutation:
		return c.Share.mutate(ctx, m)
	case *StoragePolicyMutation:
		return c.StoragePolicy.mutate(ctx, m)
	case *TaskMutation:
		return c.Task.mutate(ctx, m)
	case *UserMutation:
		return c.User.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("ent: unknown mutation type %T", m)
	}
}

// DavAccountClient is a client for the DavAccount schema.
type DavAccountClient struct {
	config
}

// NewDavAccountClient returns a client for the DavAccount from the given config.
func NewDavAccountClient(c config) *DavAccountClient {
	return &DavAccountClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `davaccount.Hooks(f(g(h())))`.
func (c *DavAccountClient) Use(hooks ...Hook) {
	c.hooks.DavAccount = append(c.hooks.DavAccount, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `davaccount.Intercept(f(g(h())))`.
func (c *DavAccountClient) Intercept(interceptors ...Interceptor) {
	c.inters.DavAccount = append(c.inters.DavAccount, interceptors...)
}

// Create returns a builder for creating a DavAccount entity.
func (c *DavAccountClient) Create() *DavAccountCreate {
	mutation := newDavAccountMutation(c.config, OpCreate)
	return &DavAccountCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of DavAccount entities.
func (c *DavAccountClient) CreateBulk(builders ...*DavAccountCreate) *DavAccountCreateBulk {
	return &DavAccountCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DavAccountClient) MapCreateBulk(slice any, setFunc func(*DavAccountCreate, int)) *DavAccountCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DavAccountCreateBulk{err: fmt.Errorf("calling to DavAccountClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DavAccountCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DavAccountCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for DavAccount.
func (c *DavAccountClient) Update() *DavAccountUpdate {
	mutation := newDavAccountMutation(c.config, OpUpdate)
	return &DavAccountUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DavAccountClient) UpdateOne(da *DavAccount) *DavAccountUpdateOne {
	mutation := newDavAccountMutation(c.config, OpUpdateOne, withDavAccount(da))
	return &DavAccountUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DavAccountClient) UpdateOneID(id int) *DavAccountUpdateOne {
	mutation := newDavAccountMutation(c.config, OpUpdateOne, withDavAccountID(id))
	return &DavAccountUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for DavAccount.
func (c *DavAccountClient) Delete() *DavAccountDelete {
	mutation := newDavAccountMutation(c.config, OpDelete)
	return &DavAccountDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DavAccountClient) DeleteOne(da *DavAccount) *DavAccountDeleteOne {
	return c.DeleteOneID(da.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DavAccountClient) DeleteOneID(id int) *DavAccountDeleteOne {
	builder := c.Delete().Where(davaccount.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DavAccountDeleteOne{builder}
}

// Query returns a query builder for DavAccount.
func (c *DavAccountClient) Query() *DavAccountQuery {
	return &DavAccountQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDavAccount},
		inters: c.Interceptors(),
	}
}

// Get returns a DavAccount entity by its id.
func (c *DavAccountClient) Get(ctx context.Context, id int) (*DavAccount, error) {
	return c.Query().Where(davaccount.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DavAccountClient) GetX(ctx context.Context, id int) *DavAccount {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryOwner queries the owner edge of a DavAccount.
func (c *DavAccountClient) QueryOwner(da *DavAccount) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := da.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(davaccount.Table, davaccount.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, davaccount.OwnerTable, davaccount.OwnerColumn),
		)
		fromV = sqlgraph.Neighbors(da.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *DavAccountClient) Hooks() []Hook {
	hooks := c.hooks.DavAccount
	return append(hooks[:len(hooks):len(hooks)], davaccount.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *DavAccountClient) Interceptors() []Interceptor {
	inters := c.inters.DavAccount
	return append(inters[:len(inters):len(inters)], davaccount.Interceptors[:]...)
}

func (c *DavAccountClient) mutate(ctx context.Context, m *DavAccountMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DavAccountCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DavAccountUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DavAccountUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DavAccountDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown DavAccount mutation op: %q", m.Op())
	}
}

// DirectLinkClient is a client for the DirectLink schema.
type DirectLinkClient struct {
	config
}

// NewDirectLinkClient returns a client for the DirectLink from the given config.
func NewDirectLinkClient(c config) *DirectLinkClient {
	return &DirectLinkClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `directlink.Hooks(f(g(h())))`.
func (c *DirectLinkClient) Use(hooks ...Hook) {
	c.hooks.DirectLink = append(c.hooks.DirectLink, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `directlink.Intercept(f(g(h())))`.
func (c *DirectLinkClient) Intercept(interceptors ...Interceptor) {
	c.inters.DirectLink = append(c.inters.DirectLink, interceptors...)
}

// Create returns a builder for creating a DirectLink entity.
func (c *DirectLinkClient) Create() *DirectLinkCreate {
	mutation := newDirectLinkMutation(c.config, OpCreate)
	return &DirectLinkCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of DirectLink entities.
func (c *DirectLinkClient) CreateBulk(builders ...*DirectLinkCreate) *DirectLinkCreateBulk {
	return &DirectLinkCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DirectLinkClient) MapCreateBulk(slice any, setFunc func(*DirectLinkCreate, int)) *DirectLinkCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DirectLinkCreateBulk{err: fmt.Errorf("calling to DirectLinkClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DirectLinkCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DirectLinkCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for DirectLink.
func (c *DirectLinkClient) Update() *DirectLinkUpdate {
	mutation := newDirectLinkMutation(c.config, OpUpdate)
	return &DirectLinkUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DirectLinkClient) UpdateOne(dl *DirectLink) *DirectLinkUpdateOne {
	mutation := newDirectLinkMutation(c.config, OpUpdateOne, withDirectLink(dl))
	return &DirectLinkUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DirectLinkClient) UpdateOneID(id int) *DirectLinkUpdateOne {
	mutation := newDirectLinkMutation(c.config, OpUpdateOne, withDirectLinkID(id))
	return &DirectLinkUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for DirectLink.
func (c *DirectLinkClient) Delete() *DirectLinkDelete {
	mutation := newDirectLinkMutation(c.config, OpDelete)
	return &DirectLinkDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DirectLinkClient) DeleteOne(dl *DirectLink) *DirectLinkDeleteOne {
	return c.DeleteOneID(dl.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DirectLinkClient) DeleteOneID(id int) *DirectLinkDeleteOne {
	builder := c.Delete().Where(directlink.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DirectLinkDeleteOne{builder}
}

// Query returns a query builder for DirectLink.
func (c *DirectLinkClient) Query() *DirectLinkQuery {
	return &DirectLinkQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDirectLink},
		inters: c.Interceptors(),
	}
}

// Get returns a DirectLink entity by its id.
func (c *DirectLinkClient) Get(ctx context.Context, id int) (*DirectLink, error) {
	return c.Query().Where(directlink.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DirectLinkClient) GetX(ctx context.Context, id int) *DirectLink {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryFile queries the file edge of a DirectLink.
func (c *DirectLinkClient) QueryFile(dl *DirectLink) *FileQuery {
	query := (&FileClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := dl.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(directlink.Table, directlink.FieldID, id),
			sqlgraph.To(file.Table, file.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, directlink.FileTable, directlink.FileColumn),
		)
		fromV = sqlgraph.Neighbors(dl.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *DirectLinkClient) Hooks() []Hook {
	hooks := c.hooks.DirectLink
	return append(hooks[:len(hooks):len(hooks)], directlink.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *DirectLinkClient) Interceptors() []Interceptor {
	inters := c.inters.DirectLink
	return append(inters[:len(inters):len(inters)], directlink.Interceptors[:]...)
}

func (c *DirectLinkClient) mutate(ctx context.Context, m *DirectLinkMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DirectLinkCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DirectLinkUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DirectLinkUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DirectLinkDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown DirectLink mutation op: %q", m.Op())
	}
}

// EntityClient is a client for the Entity schema.
type EntityClient struct {
	config
}

// NewEntityClient returns a client for the Entity from the given config.
func NewEntityClient(c config) *EntityClient {
	return &EntityClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `entity.Hooks(f(g(h())))`.
func (c *EntityClient) Use(hooks ...Hook) {
	c.hooks.Entity = append(c.hooks.Entity, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `entity.Intercept(f(g(h())))`.
func (c *EntityClient) Intercept(interceptors ...Interceptor) {
	c.inters.Entity = append(c.inters.Entity, interceptors...)
}

// Create returns a builder for creating a Entity entity.
func (c *EntityClient) Create() *EntityCreate {
	mutation := newEntityMutation(c.config, OpCreate)
	return &EntityCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Entity entities.
func (c *EntityClient) CreateBulk(builders ...*EntityCreate) *EntityCreateBulk {
	return &EntityCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *EntityClient) MapCreateBulk(slice any, setFunc func(*EntityCreate, int)) *EntityCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &EntityCreateBulk{err: fmt.Errorf("calling to EntityClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*EntityCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &EntityCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Entity.
func (c *EntityClient) Update() *EntityUpdate {
	mutation := newEntityMutation(c.config, OpUpdate)
	return &EntityUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *EntityClient) UpdateOne(e *Entity) *EntityUpdateOne {
	mutation := newEntityMutation(c.config, OpUpdateOne, withEntity(e))
	return &EntityUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *EntityClient) UpdateOneID(id int) *EntityUpdateOne {
	mutation := newEntityMutation(c.config, OpUpdateOne, withEntityID(id))
	return &EntityUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Entity.
func (c *EntityClient) Delete() *EntityDelete {
	mutation := newEntityMutation(c.config, OpDelete)
	return &EntityDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *EntityClient) DeleteOne(e *Entity) *EntityDeleteOne {
	return c.DeleteOneID(e.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *EntityClient) DeleteOneID(id int) *EntityDeleteOne {
	builder := c.Delete().Where(entity.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &EntityDeleteOne{builder}
}

// Query returns a query builder for Entity.
func (c *EntityClient) Query() *EntityQuery {
	return &EntityQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeEntity},
		inters: c.Interceptors(),
	}
}

// Get returns a Entity entity by its id.
func (c *EntityClient) Get(ctx context.Context, id int) (*Entity, error) {
	return c.Query().Where(entity.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *EntityClient) GetX(ctx context.Context, id int) *Entity {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryFile queries the file edge of a Entity.
func (c *EntityClient) QueryFile(e *Entity) *FileQuery {
	query := (&FileClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := e.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(entity.Table, entity.FieldID, id),
			sqlgraph.To(file.Table, file.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, true, entity.FileTable, entity.FilePrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(e.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUser queries the user edge of a Entity.
func (c *EntityClient) QueryUser(e *Entity) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := e.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(entity.Table, entity.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, entity.UserTable, entity.UserColumn),
		)
		fromV = sqlgraph.Neighbors(e.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryStoragePolicy queries the storage_policy edge of a Entity.
func (c *EntityClient) QueryStoragePolicy(e *Entity) *StoragePolicyQuery {
	query := (&StoragePolicyClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := e.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(entity.Table, entity.FieldID, id),
			sqlgraph.To(storagepolicy.Table, storagepolicy.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, entity.StoragePolicyTable, entity.StoragePolicyColumn),
		)
		fromV = sqlgraph.Neighbors(e.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *EntityClient) Hooks() []Hook {
	hooks := c.hooks.Entity
	return append(hooks[:len(hooks):len(hooks)], entity.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *EntityClient) Interceptors() []Interceptor {
	inters := c.inters.Entity
	return append(inters[:len(inters):len(inters)], entity.Interceptors[:]...)
}

func (c *EntityClient) mutate(ctx context.Context, m *EntityMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&EntityCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&EntityUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&EntityUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&EntityDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Entity mutation op: %q", m.Op())
	}
}

// FileClient is a client for the File schema.
type FileClient struct {
	config
}

// NewFileClient returns a client for the File from the given config.
func NewFileClient(c config) *FileClient {
	return &FileClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `file.Hooks(f(g(h())))`.
func (c *FileClient) Use(hooks ...Hook) {
	c.hooks.File = append(c.hooks.File, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `file.Intercept(f(g(h())))`.
func (c *FileClient) Intercept(interceptors ...Interceptor) {
	c.inters.File = append(c.inters.File, interceptors...)
}

// Create returns a builder for creating a File entity.
func (c *FileClient) Create() *FileCreate {
	mutation := newFileMutation(c.config, OpCreate)
	return &FileCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of File entities.
func (c *FileClient) CreateBulk(builders ...*FileCreate) *FileCreateBulk {
	return &FileCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *FileClient) MapCreateBulk(slice any, setFunc func(*FileCreate, int)) *FileCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &FileCreateBulk{err: fmt.Errorf("calling to FileClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*FileCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &FileCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for File.
func (c *FileClient) Update() *FileUpdate {
	mutation := newFileMutation(c.config, OpUpdate)
	return &FileUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *FileClient) UpdateOne(f *File) *FileUpdateOne {
	mutation := newFileMutation(c.config, OpUpdateOne, withFile(f))
	return &FileUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *FileClient) UpdateOneID(id int) *FileUpdateOne {
	mutation := newFileMutation(c.config, OpUpdateOne, withFileID(id))
	return &FileUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for File.
func (c *FileClient) Delete() *FileDelete {
	mutation := newFileMutation(c.config, OpDelete)
	return &FileDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *FileClient) DeleteOne(f *File) *FileDeleteOne {
	return c.DeleteOneID(f.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *FileClient) DeleteOneID(id int) *FileDeleteOne {
	builder := c.Delete().Where(file.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &FileDeleteOne{builder}
}

// Query returns a query builder for File.
func (c *FileClient) Query() *FileQuery {
	return &FileQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeFile},
		inters: c.Interceptors(),
	}
}

// Get returns a File entity by its id.
func (c *FileClient) Get(ctx context.Context, id int) (*File, error) {
	return c.Query().Where(file.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *FileClient) GetX(ctx context.Context, id int) *File {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryOwner queries the owner edge of a File.
func (c *FileClient) QueryOwner(f *File) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := f.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(file.Table, file.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, file.OwnerTable, file.OwnerColumn),
		)
		fromV = sqlgraph.Neighbors(f.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryStoragePolicies queries the storage_policies edge of a File.
func (c *FileClient) QueryStoragePolicies(f *File) *StoragePolicyQuery {
	query := (&StoragePolicyClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := f.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(file.Table, file.FieldID, id),
			sqlgraph.To(storagepolicy.Table, storagepolicy.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, file.StoragePoliciesTable, file.StoragePoliciesColumn),
		)
		fromV = sqlgraph.Neighbors(f.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryParent queries the parent edge of a File.
func (c *FileClient) QueryParent(f *File) *FileQuery {
	query := (&FileClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := f.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(file.Table, file.FieldID, id),
			sqlgraph.To(file.Table, file.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, file.ParentTable, file.ParentColumn),
		)
		fromV = sqlgraph.Neighbors(f.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryChildren queries the children edge of a File.
func (c *FileClient) QueryChildren(f *File) *FileQuery {
	query := (&FileClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := f.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(file.Table, file.FieldID, id),
			sqlgraph.To(file.Table, file.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, file.ChildrenTable, file.ChildrenColumn),
		)
		fromV = sqlgraph.Neighbors(f.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryMetadata queries the metadata edge of a File.
func (c *FileClient) QueryMetadata(f *File) *MetadataQuery {
	query := (&MetadataClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := f.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(file.Table, file.FieldID, id),
			sqlgraph.To(metadata.Table, metadata.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, file.MetadataTable, file.MetadataColumn),
		)
		fromV = sqlgraph.Neighbors(f.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryEntities queries the entities edge of a File.
func (c *FileClient) QueryEntities(f *File) *EntityQuery {
	query := (&EntityClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := f.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(file.Table, file.FieldID, id),
			sqlgraph.To(entity.Table, entity.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, false, file.EntitiesTable, file.EntitiesPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(f.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryShares queries the shares edge of a File.
func (c *FileClient) QueryShares(f *File) *ShareQuery {
	query := (&ShareClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := f.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(file.Table, file.FieldID, id),
			sqlgraph.To(share.Table, share.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, file.SharesTable, file.SharesColumn),
		)
		fromV = sqlgraph.Neighbors(f.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryDirectLinks queries the direct_links edge of a File.
func (c *FileClient) QueryDirectLinks(f *File) *DirectLinkQuery {
	query := (&DirectLinkClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := f.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(file.Table, file.FieldID, id),
			sqlgraph.To(directlink.Table, directlink.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, file.DirectLinksTable, file.DirectLinksColumn),
		)
		fromV = sqlgraph.Neighbors(f.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *FileClient) Hooks() []Hook {
	hooks := c.hooks.File
	return append(hooks[:len(hooks):len(hooks)], file.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *FileClient) Interceptors() []Interceptor {
	inters := c.inters.File
	return append(inters[:len(inters):len(inters)], file.Interceptors[:]...)
}

func (c *FileClient) mutate(ctx context.Context, m *FileMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&FileCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&FileUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&FileUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&FileDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown File mutation op: %q", m.Op())
	}
}

// GroupClient is a client for the Group schema.
type GroupClient struct {
	config
}

// NewGroupClient returns a client for the Group from the given config.
func NewGroupClient(c config) *GroupClient {
	return &GroupClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `group.Hooks(f(g(h())))`.
func (c *GroupClient) Use(hooks ...Hook) {
	c.hooks.Group = append(c.hooks.Group, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `group.Intercept(f(g(h())))`.
func (c *GroupClient) Intercept(interceptors ...Interceptor) {
	c.inters.Group = append(c.inters.Group, interceptors...)
}

// Create returns a builder for creating a Group entity.
func (c *GroupClient) Create() *GroupCreate {
	mutation := newGroupMutation(c.config, OpCreate)
	return &GroupCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Group entities.
func (c *GroupClient) CreateBulk(builders ...*GroupCreate) *GroupCreateBulk {
	return &GroupCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *GroupClient) MapCreateBulk(slice any, setFunc func(*GroupCreate, int)) *GroupCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &GroupCreateBulk{err: fmt.Errorf("calling to GroupClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*GroupCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &GroupCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Group.
func (c *GroupClient) Update() *GroupUpdate {
	mutation := newGroupMutation(c.config, OpUpdate)
	return &GroupUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *GroupClient) UpdateOne(gr *Group) *GroupUpdateOne {
	mutation := newGroupMutation(c.config, OpUpdateOne, withGroup(gr))
	return &GroupUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *GroupClient) UpdateOneID(id int) *GroupUpdateOne {
	mutation := newGroupMutation(c.config, OpUpdateOne, withGroupID(id))
	return &GroupUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Group.
func (c *GroupClient) Delete() *GroupDelete {
	mutation := newGroupMutation(c.config, OpDelete)
	return &GroupDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *GroupClient) DeleteOne(gr *Group) *GroupDeleteOne {
	return c.DeleteOneID(gr.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *GroupClient) DeleteOneID(id int) *GroupDeleteOne {
	builder := c.Delete().Where(group.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &GroupDeleteOne{builder}
}

// Query returns a query builder for Group.
func (c *GroupClient) Query() *GroupQuery {
	return &GroupQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeGroup},
		inters: c.Interceptors(),
	}
}

// Get returns a Group entity by its id.
func (c *GroupClient) Get(ctx context.Context, id int) (*Group, error) {
	return c.Query().Where(group.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *GroupClient) GetX(ctx context.Context, id int) *Group {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryUsers queries the users edge of a Group.
func (c *GroupClient) QueryUsers(gr *Group) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := gr.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(group.Table, group.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, group.UsersTable, group.UsersColumn),
		)
		fromV = sqlgraph.Neighbors(gr.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryStoragePolicies queries the storage_policies edge of a Group.
func (c *GroupClient) QueryStoragePolicies(gr *Group) *StoragePolicyQuery {
	query := (&StoragePolicyClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := gr.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(group.Table, group.FieldID, id),
			sqlgraph.To(storagepolicy.Table, storagepolicy.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, group.StoragePoliciesTable, group.StoragePoliciesColumn),
		)
		fromV = sqlgraph.Neighbors(gr.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *GroupClient) Hooks() []Hook {
	hooks := c.hooks.Group
	return append(hooks[:len(hooks):len(hooks)], group.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *GroupClient) Interceptors() []Interceptor {
	inters := c.inters.Group
	return append(inters[:len(inters):len(inters)], group.Interceptors[:]...)
}

func (c *GroupClient) mutate(ctx context.Context, m *GroupMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&GroupCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&GroupUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&GroupUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&GroupDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Group mutation op: %q", m.Op())
	}
}

// MetadataClient is a client for the Metadata schema.
type MetadataClient struct {
	config
}

// NewMetadataClient returns a client for the Metadata from the given config.
func NewMetadataClient(c config) *MetadataClient {
	return &MetadataClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `metadata.Hooks(f(g(h())))`.
func (c *MetadataClient) Use(hooks ...Hook) {
	c.hooks.Metadata = append(c.hooks.Metadata, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `metadata.Intercept(f(g(h())))`.
func (c *MetadataClient) Intercept(interceptors ...Interceptor) {
	c.inters.Metadata = append(c.inters.Metadata, interceptors...)
}

// Create returns a builder for creating a Metadata entity.
func (c *MetadataClient) Create() *MetadataCreate {
	mutation := newMetadataMutation(c.config, OpCreate)
	return &MetadataCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Metadata entities.
func (c *MetadataClient) CreateBulk(builders ...*MetadataCreate) *MetadataCreateBulk {
	return &MetadataCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *MetadataClient) MapCreateBulk(slice any, setFunc func(*MetadataCreate, int)) *MetadataCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &MetadataCreateBulk{err: fmt.Errorf("calling to MetadataClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*MetadataCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &MetadataCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Metadata.
func (c *MetadataClient) Update() *MetadataUpdate {
	mutation := newMetadataMutation(c.config, OpUpdate)
	return &MetadataUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *MetadataClient) UpdateOne(m *Metadata) *MetadataUpdateOne {
	mutation := newMetadataMutation(c.config, OpUpdateOne, withMetadata(m))
	return &MetadataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *MetadataClient) UpdateOneID(id int) *MetadataUpdateOne {
	mutation := newMetadataMutation(c.config, OpUpdateOne, withMetadataID(id))
	return &MetadataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Metadata.
func (c *MetadataClient) Delete() *MetadataDelete {
	mutation := newMetadataMutation(c.config, OpDelete)
	return &MetadataDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *MetadataClient) DeleteOne(m *Metadata) *MetadataDeleteOne {
	return c.DeleteOneID(m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *MetadataClient) DeleteOneID(id int) *MetadataDeleteOne {
	builder := c.Delete().Where(metadata.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &MetadataDeleteOne{builder}
}

// Query returns a query builder for Metadata.
func (c *MetadataClient) Query() *MetadataQuery {
	return &MetadataQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeMetadata},
		inters: c.Interceptors(),
	}
}

// Get returns a Metadata entity by its id.
func (c *MetadataClient) Get(ctx context.Context, id int) (*Metadata, error) {
	return c.Query().Where(metadata.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *MetadataClient) GetX(ctx context.Context, id int) *Metadata {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryFile queries the file edge of a Metadata.
func (c *MetadataClient) QueryFile(m *Metadata) *FileQuery {
	query := (&FileClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := m.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(metadata.Table, metadata.FieldID, id),
			sqlgraph.To(file.Table, file.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, metadata.FileTable, metadata.FileColumn),
		)
		fromV = sqlgraph.Neighbors(m.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *MetadataClient) Hooks() []Hook {
	hooks := c.hooks.Metadata
	return append(hooks[:len(hooks):len(hooks)], metadata.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *MetadataClient) Interceptors() []Interceptor {
	inters := c.inters.Metadata
	return append(inters[:len(inters):len(inters)], metadata.Interceptors[:]...)
}

func (c *MetadataClient) mutate(ctx context.Context, m *MetadataMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&MetadataCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&MetadataUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&MetadataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&MetadataDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Metadata mutation op: %q", m.Op())
	}
}

// NodeClient is a client for the Node schema.
type NodeClient struct {
	config
}

// NewNodeClient returns a client for the Node from the given config.
func NewNodeClient(c config) *NodeClient {
	return &NodeClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `node.Hooks(f(g(h())))`.
func (c *NodeClient) Use(hooks ...Hook) {
	c.hooks.Node = append(c.hooks.Node, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `node.Intercept(f(g(h())))`.
func (c *NodeClient) Intercept(interceptors ...Interceptor) {
	c.inters.Node = append(c.inters.Node, interceptors...)
}

// Create returns a builder for creating a Node entity.
func (c *NodeClient) Create() *NodeCreate {
	mutation := newNodeMutation(c.config, OpCreate)
	return &NodeCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Node entities.
func (c *NodeClient) CreateBulk(builders ...*NodeCreate) *NodeCreateBulk {
	return &NodeCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *NodeClient) MapCreateBulk(slice any, setFunc func(*NodeCreate, int)) *NodeCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &NodeCreateBulk{err: fmt.Errorf("calling to NodeClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*NodeCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &NodeCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Node.
func (c *NodeClient) Update() *NodeUpdate {
	mutation := newNodeMutation(c.config, OpUpdate)
	return &NodeUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *NodeClient) UpdateOne(n *Node) *NodeUpdateOne {
	mutation := newNodeMutation(c.config, OpUpdateOne, withNode(n))
	return &NodeUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *NodeClient) UpdateOneID(id int) *NodeUpdateOne {
	mutation := newNodeMutation(c.config, OpUpdateOne, withNodeID(id))
	return &NodeUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Node.
func (c *NodeClient) Delete() *NodeDelete {
	mutation := newNodeMutation(c.config, OpDelete)
	return &NodeDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *NodeClient) DeleteOne(n *Node) *NodeDeleteOne {
	return c.DeleteOneID(n.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *NodeClient) DeleteOneID(id int) *NodeDeleteOne {
	builder := c.Delete().Where(node.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &NodeDeleteOne{builder}
}

// Query returns a query builder for Node.
func (c *NodeClient) Query() *NodeQuery {
	return &NodeQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeNode},
		inters: c.Interceptors(),
	}
}

// Get returns a Node entity by its id.
func (c *NodeClient) Get(ctx context.Context, id int) (*Node, error) {
	return c.Query().Where(node.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *NodeClient) GetX(ctx context.Context, id int) *Node {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryStoragePolicy queries the storage_policy edge of a Node.
func (c *NodeClient) QueryStoragePolicy(n *Node) *StoragePolicyQuery {
	query := (&StoragePolicyClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := n.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(node.Table, node.FieldID, id),
			sqlgraph.To(storagepolicy.Table, storagepolicy.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, node.StoragePolicyTable, node.StoragePolicyColumn),
		)
		fromV = sqlgraph.Neighbors(n.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *NodeClient) Hooks() []Hook {
	hooks := c.hooks.Node
	return append(hooks[:len(hooks):len(hooks)], node.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *NodeClient) Interceptors() []Interceptor {
	inters := c.inters.Node
	return append(inters[:len(inters):len(inters)], node.Interceptors[:]...)
}

func (c *NodeClient) mutate(ctx context.Context, m *NodeMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&NodeCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&NodeUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&NodeUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&NodeDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Node mutation op: %q", m.Op())
	}
}

// PasskeyClient is a client for the Passkey schema.
type PasskeyClient struct {
	config
}

// NewPasskeyClient returns a client for the Passkey from the given config.
func NewPasskeyClient(c config) *PasskeyClient {
	return &PasskeyClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `passkey.Hooks(f(g(h())))`.
func (c *PasskeyClient) Use(hooks ...Hook) {
	c.hooks.Passkey = append(c.hooks.Passkey, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `passkey.Intercept(f(g(h())))`.
func (c *PasskeyClient) Intercept(interceptors ...Interceptor) {
	c.inters.Passkey = append(c.inters.Passkey, interceptors...)
}

// Create returns a builder for creating a Passkey entity.
func (c *PasskeyClient) Create() *PasskeyCreate {
	mutation := newPasskeyMutation(c.config, OpCreate)
	return &PasskeyCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Passkey entities.
func (c *PasskeyClient) CreateBulk(builders ...*PasskeyCreate) *PasskeyCreateBulk {
	return &PasskeyCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *PasskeyClient) MapCreateBulk(slice any, setFunc func(*PasskeyCreate, int)) *PasskeyCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &PasskeyCreateBulk{err: fmt.Errorf("calling to PasskeyClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*PasskeyCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &PasskeyCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Passkey.
func (c *PasskeyClient) Update() *PasskeyUpdate {
	mutation := newPasskeyMutation(c.config, OpUpdate)
	return &PasskeyUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *PasskeyClient) UpdateOne(pa *Passkey) *PasskeyUpdateOne {
	mutation := newPasskeyMutation(c.config, OpUpdateOne, withPasskey(pa))
	return &PasskeyUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *PasskeyClient) UpdateOneID(id int) *PasskeyUpdateOne {
	mutation := newPasskeyMutation(c.config, OpUpdateOne, withPasskeyID(id))
	return &PasskeyUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Passkey.
func (c *PasskeyClient) Delete() *PasskeyDelete {
	mutation := newPasskeyMutation(c.config, OpDelete)
	return &PasskeyDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *PasskeyClient) DeleteOne(pa *Passkey) *PasskeyDeleteOne {
	return c.DeleteOneID(pa.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *PasskeyClient) DeleteOneID(id int) *PasskeyDeleteOne {
	builder := c.Delete().Where(passkey.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &PasskeyDeleteOne{builder}
}

// Query returns a query builder for Passkey.
func (c *PasskeyClient) Query() *PasskeyQuery {
	return &PasskeyQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePasskey},
		inters: c.Interceptors(),
	}
}

// Get returns a Passkey entity by its id.
func (c *PasskeyClient) Get(ctx context.Context, id int) (*Passkey, error) {
	return c.Query().Where(passkey.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *PasskeyClient) GetX(ctx context.Context, id int) *Passkey {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryUser queries the user edge of a Passkey.
func (c *PasskeyClient) QueryUser(pa *Passkey) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pa.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(passkey.Table, passkey.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, passkey.UserTable, passkey.UserColumn),
		)
		fromV = sqlgraph.Neighbors(pa.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *PasskeyClient) Hooks() []Hook {
	hooks := c.hooks.Passkey
	return append(hooks[:len(hooks):len(hooks)], passkey.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *PasskeyClient) Interceptors() []Interceptor {
	inters := c.inters.Passkey
	return append(inters[:len(inters):len(inters)], passkey.Interceptors[:]...)
}

func (c *PasskeyClient) mutate(ctx context.Context, m *PasskeyMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&PasskeyCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&PasskeyUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&PasskeyUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&PasskeyDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Passkey mutation op: %q", m.Op())
	}
}

// SettingClient is a client for the Setting schema.
type SettingClient struct {
	config
}

// NewSettingClient returns a client for the Setting from the given config.
func NewSettingClient(c config) *SettingClient {
	return &SettingClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `setting.Hooks(f(g(h())))`.
func (c *SettingClient) Use(hooks ...Hook) {
	c.hooks.Setting = append(c.hooks.Setting, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `setting.Intercept(f(g(h())))`.
func (c *SettingClient) Intercept(interceptors ...Interceptor) {
	c.inters.Setting = append(c.inters.Setting, interceptors...)
}

// Create returns a builder for creating a Setting entity.
func (c *SettingClient) Create() *SettingCreate {
	mutation := newSettingMutation(c.config, OpCreate)
	return &SettingCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Setting entities.
func (c *SettingClient) CreateBulk(builders ...*SettingCreate) *SettingCreateBulk {
	return &SettingCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *SettingClient) MapCreateBulk(slice any, setFunc func(*SettingCreate, int)) *SettingCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &SettingCreateBulk{err: fmt.Errorf("calling to SettingClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*SettingCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &SettingCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Setting.
func (c *SettingClient) Update() *SettingUpdate {
	mutation := newSettingMutation(c.config, OpUpdate)
	return &SettingUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *SettingClient) UpdateOne(s *Setting) *SettingUpdateOne {
	mutation := newSettingMutation(c.config, OpUpdateOne, withSetting(s))
	return &SettingUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *SettingClient) UpdateOneID(id int) *SettingUpdateOne {
	mutation := newSettingMutation(c.config, OpUpdateOne, withSettingID(id))
	return &SettingUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Setting.
func (c *SettingClient) Delete() *SettingDelete {
	mutation := newSettingMutation(c.config, OpDelete)
	return &SettingDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *SettingClient) DeleteOne(s *Setting) *SettingDeleteOne {
	return c.DeleteOneID(s.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *SettingClient) DeleteOneID(id int) *SettingDeleteOne {
	builder := c.Delete().Where(setting.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &SettingDeleteOne{builder}
}

// Query returns a query builder for Setting.
func (c *SettingClient) Query() *SettingQuery {
	return &SettingQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeSetting},
		inters: c.Interceptors(),
	}
}

// Get returns a Setting entity by its id.
func (c *SettingClient) Get(ctx context.Context, id int) (*Setting, error) {
	return c.Query().Where(setting.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *SettingClient) GetX(ctx context.Context, id int) *Setting {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *SettingClient) Hooks() []Hook {
	hooks := c.hooks.Setting
	return append(hooks[:len(hooks):len(hooks)], setting.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *SettingClient) Interceptors() []Interceptor {
	inters := c.inters.Setting
	return append(inters[:len(inters):len(inters)], setting.Interceptors[:]...)
}

func (c *SettingClient) mutate(ctx context.Context, m *SettingMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&SettingCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&SettingUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&SettingUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&SettingDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Setting mutation op: %q", m.Op())
	}
}

// ShareClient is a client for the Share schema.
type ShareClient struct {
	config
}

// NewShareClient returns a client for the Share from the given config.
func NewShareClient(c config) *ShareClient {
	return &ShareClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `share.Hooks(f(g(h())))`.
func (c *ShareClient) Use(hooks ...Hook) {
	c.hooks.Share = append(c.hooks.Share, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `share.Intercept(f(g(h())))`.
func (c *ShareClient) Intercept(interceptors ...Interceptor) {
	c.inters.Share = append(c.inters.Share, interceptors...)
}

// Create returns a builder for creating a Share entity.
func (c *ShareClient) Create() *ShareCreate {
	mutation := newShareMutation(c.config, OpCreate)
	return &ShareCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Share entities.
func (c *ShareClient) CreateBulk(builders ...*ShareCreate) *ShareCreateBulk {
	return &ShareCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *ShareClient) MapCreateBulk(slice any, setFunc func(*ShareCreate, int)) *ShareCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &ShareCreateBulk{err: fmt.Errorf("calling to ShareClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*ShareCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &ShareCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Share.
func (c *ShareClient) Update() *ShareUpdate {
	mutation := newShareMutation(c.config, OpUpdate)
	return &ShareUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *ShareClient) UpdateOne(s *Share) *ShareUpdateOne {
	mutation := newShareMutation(c.config, OpUpdateOne, withShare(s))
	return &ShareUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *ShareClient) UpdateOneID(id int) *ShareUpdateOne {
	mutation := newShareMutation(c.config, OpUpdateOne, withShareID(id))
	return &ShareUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Share.
func (c *ShareClient) Delete() *ShareDelete {
	mutation := newShareMutation(c.config, OpDelete)
	return &ShareDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *ShareClient) DeleteOne(s *Share) *ShareDeleteOne {
	return c.DeleteOneID(s.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *ShareClient) DeleteOneID(id int) *ShareDeleteOne {
	builder := c.Delete().Where(share.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &ShareDeleteOne{builder}
}

// Query returns a query builder for Share.
func (c *ShareClient) Query() *ShareQuery {
	return &ShareQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeShare},
		inters: c.Interceptors(),
	}
}

// Get returns a Share entity by its id.
func (c *ShareClient) Get(ctx context.Context, id int) (*Share, error) {
	return c.Query().Where(share.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *ShareClient) GetX(ctx context.Context, id int) *Share {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryUser queries the user edge of a Share.
func (c *ShareClient) QueryUser(s *Share) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := s.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(share.Table, share.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, share.UserTable, share.UserColumn),
		)
		fromV = sqlgraph.Neighbors(s.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryFile queries the file edge of a Share.
func (c *ShareClient) QueryFile(s *Share) *FileQuery {
	query := (&FileClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := s.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(share.Table, share.FieldID, id),
			sqlgraph.To(file.Table, file.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, share.FileTable, share.FileColumn),
		)
		fromV = sqlgraph.Neighbors(s.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *ShareClient) Hooks() []Hook {
	hooks := c.hooks.Share
	return append(hooks[:len(hooks):len(hooks)], share.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *ShareClient) Interceptors() []Interceptor {
	inters := c.inters.Share
	return append(inters[:len(inters):len(inters)], share.Interceptors[:]...)
}

func (c *ShareClient) mutate(ctx context.Context, m *ShareMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&ShareCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&ShareUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&ShareUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&ShareDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Share mutation op: %q", m.Op())
	}
}

// StoragePolicyClient is a client for the StoragePolicy schema.
type StoragePolicyClient struct {
	config
}

// NewStoragePolicyClient returns a client for the StoragePolicy from the given config.
func NewStoragePolicyClient(c config) *StoragePolicyClient {
	return &StoragePolicyClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `storagepolicy.Hooks(f(g(h())))`.
func (c *StoragePolicyClient) Use(hooks ...Hook) {
	c.hooks.StoragePolicy = append(c.hooks.StoragePolicy, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `storagepolicy.Intercept(f(g(h())))`.
func (c *StoragePolicyClient) Intercept(interceptors ...Interceptor) {
	c.inters.StoragePolicy = append(c.inters.StoragePolicy, interceptors...)
}

// Create returns a builder for creating a StoragePolicy entity.
func (c *StoragePolicyClient) Create() *StoragePolicyCreate {
	mutation := newStoragePolicyMutation(c.config, OpCreate)
	return &StoragePolicyCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of StoragePolicy entities.
func (c *StoragePolicyClient) CreateBulk(builders ...*StoragePolicyCreate) *StoragePolicyCreateBulk {
	return &StoragePolicyCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *StoragePolicyClient) MapCreateBulk(slice any, setFunc func(*StoragePolicyCreate, int)) *StoragePolicyCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &StoragePolicyCreateBulk{err: fmt.Errorf("calling to StoragePolicyClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*StoragePolicyCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &StoragePolicyCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for StoragePolicy.
func (c *StoragePolicyClient) Update() *StoragePolicyUpdate {
	mutation := newStoragePolicyMutation(c.config, OpUpdate)
	return &StoragePolicyUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *StoragePolicyClient) UpdateOne(sp *StoragePolicy) *StoragePolicyUpdateOne {
	mutation := newStoragePolicyMutation(c.config, OpUpdateOne, withStoragePolicy(sp))
	return &StoragePolicyUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *StoragePolicyClient) UpdateOneID(id int) *StoragePolicyUpdateOne {
	mutation := newStoragePolicyMutation(c.config, OpUpdateOne, withStoragePolicyID(id))
	return &StoragePolicyUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for StoragePolicy.
func (c *StoragePolicyClient) Delete() *StoragePolicyDelete {
	mutation := newStoragePolicyMutation(c.config, OpDelete)
	return &StoragePolicyDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *StoragePolicyClient) DeleteOne(sp *StoragePolicy) *StoragePolicyDeleteOne {
	return c.DeleteOneID(sp.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *StoragePolicyClient) DeleteOneID(id int) *StoragePolicyDeleteOne {
	builder := c.Delete().Where(storagepolicy.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &StoragePolicyDeleteOne{builder}
}

// Query returns a query builder for StoragePolicy.
func (c *StoragePolicyClient) Query() *StoragePolicyQuery {
	return &StoragePolicyQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeStoragePolicy},
		inters: c.Interceptors(),
	}
}

// Get returns a StoragePolicy entity by its id.
func (c *StoragePolicyClient) Get(ctx context.Context, id int) (*StoragePolicy, error) {
	return c.Query().Where(storagepolicy.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *StoragePolicyClient) GetX(ctx context.Context, id int) *StoragePolicy {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryGroups queries the groups edge of a StoragePolicy.
func (c *StoragePolicyClient) QueryGroups(sp *StoragePolicy) *GroupQuery {
	query := (&GroupClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := sp.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(storagepolicy.Table, storagepolicy.FieldID, id),
			sqlgraph.To(group.Table, group.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, storagepolicy.GroupsTable, storagepolicy.GroupsColumn),
		)
		fromV = sqlgraph.Neighbors(sp.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryFiles queries the files edge of a StoragePolicy.
func (c *StoragePolicyClient) QueryFiles(sp *StoragePolicy) *FileQuery {
	query := (&FileClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := sp.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(storagepolicy.Table, storagepolicy.FieldID, id),
			sqlgraph.To(file.Table, file.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, storagepolicy.FilesTable, storagepolicy.FilesColumn),
		)
		fromV = sqlgraph.Neighbors(sp.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryEntities queries the entities edge of a StoragePolicy.
func (c *StoragePolicyClient) QueryEntities(sp *StoragePolicy) *EntityQuery {
	query := (&EntityClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := sp.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(storagepolicy.Table, storagepolicy.FieldID, id),
			sqlgraph.To(entity.Table, entity.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, storagepolicy.EntitiesTable, storagepolicy.EntitiesColumn),
		)
		fromV = sqlgraph.Neighbors(sp.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryNode queries the node edge of a StoragePolicy.
func (c *StoragePolicyClient) QueryNode(sp *StoragePolicy) *NodeQuery {
	query := (&NodeClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := sp.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(storagepolicy.Table, storagepolicy.FieldID, id),
			sqlgraph.To(node.Table, node.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, storagepolicy.NodeTable, storagepolicy.NodeColumn),
		)
		fromV = sqlgraph.Neighbors(sp.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *StoragePolicyClient) Hooks() []Hook {
	hooks := c.hooks.StoragePolicy
	return append(hooks[:len(hooks):len(hooks)], storagepolicy.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *StoragePolicyClient) Interceptors() []Interceptor {
	inters := c.inters.StoragePolicy
	return append(inters[:len(inters):len(inters)], storagepolicy.Interceptors[:]...)
}

func (c *StoragePolicyClient) mutate(ctx context.Context, m *StoragePolicyMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&StoragePolicyCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&StoragePolicyUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&StoragePolicyUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&StoragePolicyDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown StoragePolicy mutation op: %q", m.Op())
	}
}

// TaskClient is a client for the Task schema.
type TaskClient struct {
	config
}

// NewTaskClient returns a client for the Task from the given config.
func NewTaskClient(c config) *TaskClient {
	return &TaskClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `task.Hooks(f(g(h())))`.
func (c *TaskClient) Use(hooks ...Hook) {
	c.hooks.Task = append(c.hooks.Task, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `task.Intercept(f(g(h())))`.
func (c *TaskClient) Intercept(interceptors ...Interceptor) {
	c.inters.Task = append(c.inters.Task, interceptors...)
}

// Create returns a builder for creating a Task entity.
func (c *TaskClient) Create() *TaskCreate {
	mutation := newTaskMutation(c.config, OpCreate)
	return &TaskCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Task entities.
func (c *TaskClient) CreateBulk(builders ...*TaskCreate) *TaskCreateBulk {
	return &TaskCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TaskClient) MapCreateBulk(slice any, setFunc func(*TaskCreate, int)) *TaskCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TaskCreateBulk{err: fmt.Errorf("calling to TaskClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TaskCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TaskCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Task.
func (c *TaskClient) Update() *TaskUpdate {
	mutation := newTaskMutation(c.config, OpUpdate)
	return &TaskUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TaskClient) UpdateOne(t *Task) *TaskUpdateOne {
	mutation := newTaskMutation(c.config, OpUpdateOne, withTask(t))
	return &TaskUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TaskClient) UpdateOneID(id int) *TaskUpdateOne {
	mutation := newTaskMutation(c.config, OpUpdateOne, withTaskID(id))
	return &TaskUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Task.
func (c *TaskClient) Delete() *TaskDelete {
	mutation := newTaskMutation(c.config, OpDelete)
	return &TaskDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TaskClient) DeleteOne(t *Task) *TaskDeleteOne {
	return c.DeleteOneID(t.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TaskClient) DeleteOneID(id int) *TaskDeleteOne {
	builder := c.Delete().Where(task.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TaskDeleteOne{builder}
}

// Query returns a query builder for Task.
func (c *TaskClient) Query() *TaskQuery {
	return &TaskQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTask},
		inters: c.Interceptors(),
	}
}

// Get returns a Task entity by its id.
func (c *TaskClient) Get(ctx context.Context, id int) (*Task, error) {
	return c.Query().Where(task.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TaskClient) GetX(ctx context.Context, id int) *Task {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryUser queries the user edge of a Task.
func (c *TaskClient) QueryUser(t *Task) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(task.Table, task.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, task.UserTable, task.UserColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *TaskClient) Hooks() []Hook {
	hooks := c.hooks.Task
	return append(hooks[:len(hooks):len(hooks)], task.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *TaskClient) Interceptors() []Interceptor {
	inters := c.inters.Task
	return append(inters[:len(inters):len(inters)], task.Interceptors[:]...)
}

func (c *TaskClient) mutate(ctx context.Context, m *TaskMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TaskCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TaskUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TaskUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TaskDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Task mutation op: %q", m.Op())
	}
}

// UserClient is a client for the User schema.
type UserClient struct {
	config
}

// NewUserClient returns a client for the User from the given config.
func NewUserClient(c config) *UserClient {
	return &UserClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `user.Hooks(f(g(h())))`.
func (c *UserClient) Use(hooks ...Hook) {
	c.hooks.User = append(c.hooks.User, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `user.Intercept(f(g(h())))`.
func (c *UserClient) Intercept(interceptors ...Interceptor) {
	c.inters.User = append(c.inters.User, interceptors...)
}

// Create returns a builder for creating a User entity.
func (c *UserClient) Create() *UserCreate {
	mutation := newUserMutation(c.config, OpCreate)
	return &UserCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of User entities.
func (c *UserClient) CreateBulk(builders ...*UserCreate) *UserCreateBulk {
	return &UserCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *UserClient) MapCreateBulk(slice any, setFunc func(*UserCreate, int)) *UserCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &UserCreateBulk{err: fmt.Errorf("calling to UserClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*UserCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &UserCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for User.
func (c *UserClient) Update() *UserUpdate {
	mutation := newUserMutation(c.config, OpUpdate)
	return &UserUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *UserClient) UpdateOne(u *User) *UserUpdateOne {
	mutation := newUserMutation(c.config, OpUpdateOne, withUser(u))
	return &UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *UserClient) UpdateOneID(id int) *UserUpdateOne {
	mutation := newUserMutation(c.config, OpUpdateOne, withUserID(id))
	return &UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for User.
func (c *UserClient) Delete() *UserDelete {
	mutation := newUserMutation(c.config, OpDelete)
	return &UserDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *UserClient) DeleteOne(u *User) *UserDeleteOne {
	return c.DeleteOneID(u.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *UserClient) DeleteOneID(id int) *UserDeleteOne {
	builder := c.Delete().Where(user.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &UserDeleteOne{builder}
}

// Query returns a query builder for User.
func (c *UserClient) Query() *UserQuery {
	return &UserQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeUser},
		inters: c.Interceptors(),
	}
}

// Get returns a User entity by its id.
func (c *UserClient) Get(ctx context.Context, id int) (*User, error) {
	return c.Query().Where(user.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *UserClient) GetX(ctx context.Context, id int) *User {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryGroup queries the group edge of a User.
func (c *UserClient) QueryGroup(u *User) *GroupQuery {
	query := (&GroupClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(group.Table, group.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, user.GroupTable, user.GroupColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryFiles queries the files edge of a User.
func (c *UserClient) QueryFiles(u *User) *FileQuery {
	query := (&FileClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(file.Table, file.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, user.FilesTable, user.FilesColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryDavAccounts queries the dav_accounts edge of a User.
func (c *UserClient) QueryDavAccounts(u *User) *DavAccountQuery {
	query := (&DavAccountClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(davaccount.Table, davaccount.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, user.DavAccountsTable, user.DavAccountsColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryShares queries the shares edge of a User.
func (c *UserClient) QueryShares(u *User) *ShareQuery {
	query := (&ShareClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(share.Table, share.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, user.SharesTable, user.SharesColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryPasskey queries the passkey edge of a User.
func (c *UserClient) QueryPasskey(u *User) *PasskeyQuery {
	query := (&PasskeyClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(passkey.Table, passkey.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, user.PasskeyTable, user.PasskeyColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTasks queries the tasks edge of a User.
func (c *UserClient) QueryTasks(u *User) *TaskQuery {
	query := (&TaskClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(task.Table, task.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, user.TasksTable, user.TasksColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryEntities queries the entities edge of a User.
func (c *UserClient) QueryEntities(u *User) *EntityQuery {
	query := (&EntityClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(entity.Table, entity.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, user.EntitiesTable, user.EntitiesColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *UserClient) Hooks() []Hook {
	hooks := c.hooks.User
	return append(hooks[:len(hooks):len(hooks)], user.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *UserClient) Interceptors() []Interceptor {
	inters := c.inters.User
	return append(inters[:len(inters):len(inters)], user.Interceptors[:]...)
}

func (c *UserClient) mutate(ctx context.Context, m *UserMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&UserCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&UserUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&UserDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown User mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		DavAccount, DirectLink, Entity, File, Group, Metadata, Node, Passkey, Setting,
		Share, StoragePolicy, Task, User []ent.Hook
	}
	inters struct {
		DavAccount, DirectLink, Entity, File, Group, Metadata, Node, Passkey, Setting,
		Share, StoragePolicy, Task, User []ent.Interceptor
	}
)

// ExecContext allows calling the underlying ExecContext method of the driver if it is supported by it.
// See, database/sql#DB.ExecContext for more information.
func (c *config) ExecContext(ctx context.Context, query string, args ...any) (stdsql.Result, error) {
	ex, ok := c.driver.(interface {
		ExecContext(context.Context, string, ...any) (stdsql.Result, error)
	})
	if !ok {
		return nil, fmt.Errorf("Driver.ExecContext is not supported")
	}
	return ex.ExecContext(ctx, query, args...)
}

// QueryContext allows calling the underlying QueryContext method of the driver if it is supported by it.
// See, database/sql#DB.QueryContext for more information.
func (c *config) QueryContext(ctx context.Context, query string, args ...any) (*stdsql.Rows, error) {
	q, ok := c.driver.(interface {
		QueryContext(context.Context, string, ...any) (*stdsql.Rows, error)
	})
	if !ok {
		return nil, fmt.Errorf("Driver.QueryContext is not supported")
	}
	return q.QueryContext(ctx, query, args...)
}

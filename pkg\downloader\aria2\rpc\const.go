package rpc

const (
	aria2AddURI               = "aria2.addUri"
	aria2AddTorrent           = "aria2.addTorrent"
	aria2AddMetalink          = "aria2.addMetalink"
	aria2Remove               = "aria2.remove"
	aria2ForceRemove          = "aria2.forceRemove"
	aria2Pause                = "aria2.pause"
	aria2PauseAll             = "aria2.pauseAll"
	aria2ForcePause           = "aria2.forcePause"
	aria2ForcePauseAll        = "aria2.forcePauseAll"
	aria2Unpause              = "aria2.unpause"
	aria2UnpauseAll           = "aria2.unpauseAll"
	aria2TellStatus           = "aria2.tellStatus"
	aria2GetURIs              = "aria2.getUris"
	aria2GetFiles             = "aria2.getFiles"
	aria2GetPeers             = "aria2.getPeers"
	aria2GetServers           = "aria2.getServers"
	aria2TellActive           = "aria2.tellActive"
	aria2TellWaiting          = "aria2.tellWaiting"
	aria2TellStopped          = "aria2.tellStopped"
	aria2ChangePosition       = "aria2.changePosition"
	aria2ChangeURI            = "aria2.changeUri"
	aria2GetOption            = "aria2.getOption"
	aria2ChangeOption         = "aria2.changeOption"
	aria2GetGlobalOption      = "aria2.getGlobalOption"
	aria2ChangeGlobalOption   = "aria2.changeGlobalOption"
	aria2GetGlobalStat        = "aria2.getGlobalStat"
	aria2PurgeDownloadResult  = "aria2.purgeDownloadResult"
	aria2RemoveDownloadResult = "aria2.removeDownloadResult"
	aria2GetVersion           = "aria2.getVersion"
	aria2GetSessionInfo       = "aria2.getSessionInfo"
	aria2Shutdown             = "aria2.shutdown"
	aria2ForceShutdown        = "aria2.forceShutdown"
	aria2SaveSession          = "aria2.saveSession"
	aria2Multicall            = "system.multicall"
	aria2ListMethods          = "system.listMethods"
)

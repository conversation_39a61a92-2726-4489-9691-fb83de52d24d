# Cloudreve 实名认证功能测试脚本
$baseUrl = "http://localhost:5212/api/v3"

Write-Host "=== 测试 Cloudreve 实名认证功能 ===" -ForegroundColor Green

# 1. 测试 ping 接口
Write-Host "`n1. 测试 ping 接口..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/site/ping" -Method GET
    Write-Host "✅ Ping 成功: $($response | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "❌ Ping 失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. 测试用户注册
Write-Host "`n2. 测试用户注册..." -ForegroundColor Yellow
$registerData = @{
    email = "<EMAIL>"
    password = "123456"
    language = "zh-CN"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/user" -Method POST -Body $registerData -ContentType "application/json"
    Write-Host "✅ 用户注册成功: $($response | ConvertTo-Json)" -ForegroundColor Green
    $userId = $response.data.id
} catch {
    Write-Host "❌ 用户注册失败: $($_.Exception.Message)" -ForegroundColor Red
    $errorResponse = $_.Exception.Response
    if ($errorResponse) {
        $reader = New-Object System.IO.StreamReader($errorResponse.GetResponseStream())
        $errorContent = $reader.ReadToEnd()
        Write-Host "错误详情: $errorContent" -ForegroundColor Red
    }
}

# 3. 测试用户登录
Write-Host "`n3. 测试用户登录..." -ForegroundColor Yellow
$loginData = @{
    email = "<EMAIL>"
    password = "123456"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/session/token" -Method POST -Body $loginData -ContentType "application/json"
    Write-Host "✅ 用户登录成功" -ForegroundColor Green
    $token = $response.data.token
    $headers = @{ Authorization = "Bearer $token" }
} catch {
    Write-Host "❌ 用户登录失败: $($_.Exception.Message)" -ForegroundColor Red
    $errorResponse = $_.Exception.Response
    if ($errorResponse) {
        $reader = New-Object System.IO.StreamReader($errorResponse.GetResponseStream())
        $errorContent = $reader.ReadToEnd()
        Write-Host "错误详情: $errorContent" -ForegroundColor Red
    }
    exit
}

# 4. 测试查询实名认证状态
Write-Host "`n4. 测试查询实名认证状态..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/user/realname" -Method GET -Headers $headers
    Write-Host "✅ 查询实名认证状态成功: $($response | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "❌ 查询实名认证状态失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. 测试提交实名认证
Write-Host "`n5. 测试提交实名认证..." -ForegroundColor Yellow
$realnameData = @{
    real_name = "张三"
    id_card = "110101199001011234"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/user/realname" -Method POST -Body $realnameData -ContentType "application/json" -Headers $headers
    Write-Host "✅ 提交实名认证成功: $($response | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "❌ 提交实名认证失败: $($_.Exception.Message)" -ForegroundColor Red
    $errorResponse = $_.Exception.Response
    if ($errorResponse) {
        $reader = New-Object System.IO.StreamReader($errorResponse.GetResponseStream())
        $errorContent = $reader.ReadToEnd()
        Write-Host "错误详情: $errorContent" -ForegroundColor Red
    }
}

# 6. 再次查询实名认证状态
Write-Host "`n6. 再次查询实名认证状态..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/user/realname" -Method GET -Headers $headers
    Write-Host "✅ 查询实名认证状态成功: $($response | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "❌ 查询实名认证状态失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 7. 测试未认证用户上传文件
Write-Host "`n7. 测试未认证用户上传文件..." -ForegroundColor Yellow
$uploadData = @{
    path = "/"
    size = 100
    name = "test.txt"
    policy_id = 1
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/file/upload" -Method PUT -Body $uploadData -ContentType "application/json" -Headers $headers
    Write-Host "❌ 上传文件成功（应该被拒绝）: $($response | ConvertTo-Json)" -ForegroundColor Red
} catch {
    Write-Host "✅ 上传文件被拒绝（符合预期）: $($_.Exception.Message)" -ForegroundColor Green
    $errorResponse = $_.Exception.Response
    if ($errorResponse) {
        $reader = New-Object System.IO.StreamReader($errorResponse.GetResponseStream())
        $errorContent = $reader.ReadToEnd()
        Write-Host "错误详情: $errorContent" -ForegroundColor Yellow
    }
}

Write-Host "`n🎉 实名认证功能测试完成！" -ForegroundColor Green

#!/usr/bin/env python3
"""
Cloudreve 实名认证功能测试脚本
"""

import requests
import json
import time

BASE_URL = "http://localhost:5212"
API_BASE = f"{BASE_URL}/api/v3"

def test_user_register():
    """测试用户注册"""
    print("=== 测试用户注册 ===")
    
    register_data = {
        "email": "<EMAIL>",
        "password": "123456",
        "language": "zh-CN"
    }
    
    response = requests.post(f"{API_BASE}/user", json=register_data)
    print(f"注册响应状态码: {response.status_code}")
    print(f"注册响应内容: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        if result.get("code") == 0:
            print("✅ 用户注册成功")
            return result.get("data")
        else:
            print(f"❌ 用户注册失败: {result.get('msg', '未知错误')}")
    return None

def test_user_login():
    """测试用户登录"""
    print("\n=== 测试用户登录 ===")
    
    login_data = {
        "email": "<EMAIL>",
        "password": "123456"
    }
    
    response = requests.post(f"{API_BASE}/session/token", json=login_data)
    print(f"登录响应状态码: {response.status_code}")
    print(f"登录响应内容: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        if result.get("code") == 0:
            print("✅ 用户登录成功")
            return result.get("data", {}).get("token")
        else:
            print(f"❌ 用户登录失败: {result.get('msg', '未知错误')}")
    return None

def test_realname_auth_status(token):
    """测试查询实名认证状态"""
    print("\n=== 测试查询实名认证状态 ===")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{API_BASE}/user/realname", headers=headers)
    print(f"查询状态响应码: {response.status_code}")
    print(f"查询状态响应: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        if result.get("code") == 0:
            print("✅ 查询实名认证状态成功")
            return result.get("data")
        else:
            print(f"❌ 查询实名认证状态失败: {result.get('msg', '未知错误')}")
    return None

def test_submit_realname_auth(token):
    """测试提交实名认证"""
    print("\n=== 测试提交实名认证 ===")
    
    realname_data = {
        "real_name": "张三",
        "id_card": "110101199001011234"
    }
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.post(f"{API_BASE}/user/realname", json=realname_data, headers=headers)
    print(f"提交认证响应码: {response.status_code}")
    print(f"提交认证响应: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        if result.get("code") == 0:
            print("✅ 提交实名认证成功")
            return True
        else:
            print(f"❌ 提交实名认证失败: {result.get('msg', '未知错误')}")
    return False

def test_file_upload_without_auth(token):
    """测试未认证用户上传文件"""
    print("\n=== 测试未认证用户上传文件 ===")
    
    # 创建上传会话
    upload_session_data = {
        "path": "/",
        "size": 100,
        "name": "test.txt",
        "policy_id": 1
    }
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.put(f"{API_BASE}/file/upload", json=upload_session_data, headers=headers)
    print(f"创建上传会话响应码: {response.status_code}")
    print(f"创建上传会话响应: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        if result.get("code") == 40088:  # 实名认证错误码
            print("✅ 实名认证检查生效，未认证用户无法上传文件")
            return True
        else:
            print(f"❌ 实名认证检查未生效: {result.get('msg', '未知错误')}")
    return False

def main():
    """主测试函数"""
    print("🚀 开始测试 Cloudreve 实名认证功能")
    
    # 1. 测试用户注册
    user_data = test_user_register()
    
    # 2. 测试用户登录
    token = test_user_login()
    if not token:
        print("❌ 无法获取登录令牌，测试终止")
        return
    
    # 3. 测试查询实名认证状态（应该为空）
    auth_status = test_realname_auth_status(token)
    
    # 4. 测试未认证用户上传文件（应该被拒绝）
    test_file_upload_without_auth(token)
    
    # 5. 测试提交实名认证
    if test_submit_realname_auth(token):
        # 6. 再次查询实名认证状态（应该显示待审核）
        print("\n=== 再次查询实名认证状态 ===")
        auth_status = test_realname_auth_status(token)
    
    print("\n🎉 实名认证功能测试完成！")

if __name__ == "__main__":
    main()

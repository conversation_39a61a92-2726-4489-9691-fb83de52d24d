package controllers

import (
	"time"

	"github.com/cloudreve/Cloudreve/v4/application/dependency"
	"github.com/cloudreve/Cloudreve/v4/inventory"
	"github.com/cloudreve/Cloudreve/v4/pkg/serializer"
	"github.com/cloudreve/Cloudreve/v4/service/user"
	"github.com/gin-gonic/gin"
)

// VerificationData 认证数据结构
type VerificationData struct {
	RealName     string    `json:"real_name"`
	IDCard       string    `json:"id_card"`
	SubmittedAt  time.Time `json:"submitted_at"`
	Status       string    `json:"status"`
	VerifiedAt   *time.Time `json:"verified_at,omitempty"`
	RejectReason string    `json:"reject_reason,omitempty"`
}

// SubmitVerification 提交实名认证
func SubmitVerification(c *gin.Context) {
	service := ParametersFromContext[*user.VerificationService](c, user.VerificationServiceParamsCtx{})
	resp, err := service.Submit(c)
	if err != nil {
		c.JSON(200, serializer.Err(c, err))
		return
	}

	c.JSON(200, serializer.Response{
		Code: 0,
		Msg:  "认证提交成功",
		Data: resp,
	})
}

// GetVerificationStatus 获取认证状态
func GetVerificationStatus(c *gin.Context) {
	currentUser := inventory.UserFromContext(c)
	if currentUser == nil {
		c.JSON(200, serializer.NewError(serializer.CodeCredentialInvalid, "用户未登录", nil))
		return
	}

	dep := dependency.FromContext(c)
	userClient := dep.UserClient()

	// 重新查询用户以获取最新的认证信息
	user, err := userClient.GetByID(c, currentUser.ID)
	if err != nil {
		c.JSON(200, serializer.NewError(serializer.CodeDBError, "查询用户信息失败", err))
		return
	}

	// 构建响应数据
	var response map[string]interface{}

	if user.Settings.RealNameAuth != nil && user.Settings.RealNameAuth.Status == "approved" {
		// 已认证用户，返回脱敏信息
		response = map[string]interface{}{
			"verified":    true,
			"real_name":   maskName(user.Settings.RealNameAuth.RealName),
			"id_card":     maskIDCard(user.Settings.RealNameAuth.IDCard),
			"verified_at": user.Settings.RealNameAuth.ApprovedAt,
		}
	} else if user.Settings.RealNameAuth != nil {
		// 有认证数据但未通过
		response = map[string]interface{}{
			"verified":      false,
			"real_name":     maskName(user.Settings.RealNameAuth.RealName),
			"id_card":       maskIDCard(user.Settings.RealNameAuth.IDCard),
			"status":        user.Settings.RealNameAuth.Status,
			"submitted_at":  user.Settings.RealNameAuth.SubmittedAt,
			"reject_reason": user.Settings.RealNameAuth.RejectReason,
		}
	} else {
		// 未提交认证
		response = map[string]interface{}{
			"verified": false,
			"status":   "not_submitted",
		}
	}

	c.JSON(200, serializer.Response{
		Code: 0,
		Data: response,
	})
}

// ApproveVerification 管理员审核通过实名认证
func ApproveVerification(c *gin.Context) {
	service := ParametersFromContext[*user.ApproveVerificationService](c, user.ApproveVerificationServiceParamsCtx{})
	err := service.Approve(c)
	if err != nil {
		c.JSON(200, serializer.Err(c, err))
		return
	}

	c.JSON(200, serializer.Response{
		Code: 0,
		Msg:  "认证审核通过",
	})
}

// RejectVerification 管理员拒绝实名认证
func RejectVerification(c *gin.Context) {
	service := ParametersFromContext[*user.RejectVerificationService](c, user.RejectVerificationServiceParamsCtx{})
	err := service.Reject(c)
	if err != nil {
		c.JSON(200, serializer.Err(c, err))
		return
	}

	c.JSON(200, serializer.Response{
		Code: 0,
		Msg:  "认证已拒绝",
	})
}

// ListPendingVerifications 管理员查看待审核的实名认证列表
func ListPendingVerifications(c *gin.Context) {
	service := ParametersFromContext[*user.ListVerificationService](c, user.ListVerificationServiceParamsCtx{})
	resp, err := service.List(c)
	if err != nil {
		c.JSON(200, serializer.Err(c, err))
		return
	}

	c.JSON(200, serializer.Response{
		Code: 0,
		Data: resp,
	})
}

// maskName 脱敏姓名
func maskName(name string) string {
	if len(name) == 0 {
		return ""
	}
	if len(name) == 1 {
		return "*"
	}
	if len(name) == 2 {
		return string([]rune(name)[0]) + "*"
	}
	runes := []rune(name)
	result := string(runes[0])
	for i := 1; i < len(runes)-1; i++ {
		result += "*"
	}
	result += string(runes[len(runes)-1])
	return result
}

// maskIDCard 脱敏身份证号
func maskIDCard(idCard string) string {
	if len(idCard) < 8 {
		return "****"
	}
	return idCard[:4] + "**********" + idCard[len(idCard)-4:]
}
